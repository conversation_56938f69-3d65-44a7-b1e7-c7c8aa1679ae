<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Feature;

class FeatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Feature::firstOrCreate(['name' => 'sugar_partner'], [
            'label' => 'Sugar Partner',
            'description' => 'Allow users to specify interest in sugar partner relationships',
            'is_enabled' => false,
            'options' => [
                'sugar_daddy' => 'Sugar Daddy',
                'sugar_mommy' => 'Sugar Mommy',
                'sugar_companion_female' => 'Sugerbabe',
                'sugar_companion_male' => 'Sugerboy'
            ]
        ]);

        Feature::firstOrCreate(['name' => 'subscription_model'], [
            'label' => 'Subscription Model',
            'description' => 'Enable subscription-based access to Time Spending service. When enabled, users must purchase subscriptions to use Time Spending features.',
            'is_enabled' => false,
            'options' => [
                'subscription_required' => 'Subscription Required for Time Spending',
                'profile_visibility' => 'Profile Visibility Based on Subscription',
                'auto_enable_features' => 'Auto-enable Related Features'
            ]
        ]);

        Feature::firstOrCreate(['name' => 'time_spending'], [
            'label' => 'Time Spending',
            'description' => 'Allow users to set hourly rates for time spending services. When enabled, users can set their hourly rate in INR and offer time spending services.',
            'is_enabled' => false,
            'options' => null
        ]);

        Feature::firstOrCreate(['name' => 'gallery'], [
            'label' => 'Gallery',
            'description' => 'Allow users to upload and manage multiple images in their gallery. When enabled, users can upload, reorder, and manage their profile images.',
            'is_enabled' => false,
            'options' => null
        ]);

        Feature::firstOrCreate(['name' => 'partner_swapping'], [
            'label' => 'Couple Activity',
            'description' => 'Allow couples to participate in couple-friendly activities and events. When enabled, couples can join special activities designed for pairs.',
            'is_enabled' => false,
            'options' => null
        ]);

        Feature::firstOrCreate(['name' => 'meeting_events'], [
            'label' => 'Meeting Events',
            'description' => 'Allow users to join organized meeting events and activities. When enabled, users can view and participate in meeting events.',
            'is_enabled' => true,
            'options' => null
        ]);

        Feature::firstOrCreate(['name' => 'notifications'], [
            'label' => 'Notifications',
            'description' => 'Enable push notifications and in-app notifications for users. When enabled, users receive notifications for various activities.',
            'is_enabled' => true,
            'options' => null
        ]);

        Feature::firstOrCreate(['name' => 'user_verification'], [
            'label' => 'User Verification',
            'description' => 'Enable user verification system with profile verification badges. When enabled, users can get verified profiles.',
            'is_enabled' => false,
            'is_hidden' => true, // Hidden from admin features page
            'options' => [
                'photo_verification' => 'Photo Verification',
                'phone_verification' => 'Phone Verification',
                'email_verification' => 'Email Verification'
            ]
        ]);

        Feature::firstOrCreate(['name' => 'premium_membership'], [
            'label' => 'Premium Membership',
            'description' => 'Enable premium membership features with enhanced privileges. When enabled, users can purchase premium memberships.',
            'is_enabled' => false,
            'is_hidden' => true, // Hidden from admin features page
            'options' => [
                'unlimited_likes' => 'Unlimited Likes',
                'advanced_filters' => 'Advanced Filters',
                'priority_support' => 'Priority Support',
                'ad_free_experience' => 'Ad-Free Experience'
            ]
        ]);

        Feature::firstOrCreate(['name' => 'chat_system'], [
            'label' => 'Chat System',
            'description' => 'Enable real-time chat functionality between users. Chat is automatically enabled when Time Spending feature is active.',
            'is_enabled' => true,
            'options' => null
        ]);

        Feature::firstOrCreate(['name' => 'location_services'], [
            'label' => 'Location Services',
            'description' => 'Enable location-based features including location detection and matching. Essential for time spending and meeting events.',
            'is_enabled' => true,
            'is_hidden' => true, // Hidden from admin features page
            'options' => null
        ]);

        Feature::firstOrCreate(['name' => 'privacy_controls'], [
            'label' => 'Privacy Controls',
            'description' => 'Enable advanced privacy settings for user profiles. Allows users to control visibility of their information.',
            'is_enabled' => true,
            'is_hidden' => true, // Hidden from admin features page
            'options' => [
                'public_profile' => 'Public Profile Visibility',
                'contact_visibility' => 'Contact Number Visibility',
                'dob_visibility' => 'Date of Birth Visibility',
                'interests_visibility' => 'Interests Visibility',
                'expectations_visibility' => 'Expectations Visibility',
                'gallery_visibility' => 'Gallery Images Visibility'
            ]
        ]);

        Feature::firstOrCreate(['name' => 'meeting_verification'], [
            'label' => 'Meeting Verification',
            'description' => 'Enable photo and location verification for meetings. Users must take photos with location data at meeting start and end.',
            'is_enabled' => false,
            'options' => [
                'require_location' => 'Require Location Data',
                'camera_only' => 'Camera Only (No Gallery)',
                'auto_duration_calc' => 'Auto Duration Calculation',
                'verification_notifications' => 'Verification Notifications'
            ]
        ]);

        Feature::firstOrCreate(['name' => 'rating_review_system'], [
            'label' => 'Rating & Review System',
            'description' => 'Enable users to rate and review each other after completed meetings. Works when meeting verification is disabled.',
            'is_enabled' => true,
            'options' => [
                'allow_anonymous_reviews' => 'Allow Anonymous Reviews',
                'require_text_review' => 'Require Text Review',
                'admin_approval_required' => 'Admin Approval Required',
                'auto_review_prompts' => 'Automatic Review Prompts'
            ]
        ]);

    }
}
