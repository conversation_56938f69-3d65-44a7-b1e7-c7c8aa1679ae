<?php

namespace App\Http\Controllers;

use App\Models\Feature;
use App\Models\SugarPartnerExchange;
use App\Models\SugarPartnerExchangePayment;
use App\Models\SugarPartnerRejection;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class SugarPartnerExchangeController extends Controller
{
    /**
     * Show exchange payment page for a user.
     */
    public function showPayment(SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to view this exchange.');
        }

        // Check if user has already paid
        if ($exchange->userHasPaid($user->id)) {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('info', 'You have already paid for this exchange.');
        }

        // Check if exchange is in correct status
        if ($exchange->status !== 'pending_payment') {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('info', 'This exchange is no longer accepting payments.');
        }

        $otherUser = $exchange->getOtherUser($user->id);
        $razorpayKey = Setting::get('razorpay_key_id');

        return view('sugar-partner.exchange.payment', compact(
            'exchange',
            'user',
            'otherUser',
            'razorpayKey'
        ));
    }

    /**
     * Process payment for exchange.
     */
    public function processPayment(Request $request, SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to pay for this exchange.');
        }

        // Check if user has already paid
        if ($exchange->userHasPaid($user->id)) {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'You have already paid for this exchange.');
        }

        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:razorpay,wallet',
            'razorpay_payment_id' => 'required_if:payment_method,razorpay|string',
            'razorpay_order_id' => 'required_if:payment_method,razorpay|string',
            'razorpay_signature' => 'required_if:payment_method,razorpay|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();
        try {
            if ($request->payment_method === 'wallet') {
                $this->processWalletPayment($exchange, $user);
            } else {
                $this->processRazorpayPayment($exchange, $user, $request);
            }

            DB::commit();

            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('success', 'Payment completed successfully!');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Payment failed: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show exchange status page.
     */
    public function showStatus(SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to view this exchange.');
        }

        $otherUser = $exchange->getOtherUser($user->id);
        $userHasPaid = $exchange->userHasPaid($user->id);
        $otherUserHasPaid = $exchange->userHasPaid($otherUser->id);
        $bothPaid = $exchange->bothUsersPaid();

        // Load user's payment if exists
        $userPayment = $exchange->payments()
            ->where('user_id', $user->id)
            ->first();

        // Load user's response if exists
        $userResponse = $exchange->getUserResponse($user->id);

        return view('sugar-partner.exchange.status', compact(
            'exchange',
            'user',
            'otherUser',
            'userHasPaid',
            'otherUserHasPaid',
            'bothPaid',
            'userPayment',
            'userResponse'
        ));
    }

    /**
     * Show other user's profile after payment.
     */
    public function showProfile(SugarPartnerExchange $exchange, User $profileUser)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to view this exchange.');
        }

        // Verify the profile user is the other user in the exchange
        $otherUser = $exchange->getOtherUser($user->id);
        if ($profileUser->id !== $otherUser->id) {
            abort(403, 'Invalid profile access.');
        }

        // Verify both users have paid
        if (!$exchange->bothUsersPaid()) {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'Both users must complete payment before viewing profiles.');
        }

        // Mark profiles as viewed if this is the first time
        if ($exchange->status === 'payment_completed') {
            $exchange->markProfilesViewed();
        }

        // Load profile user with all necessary data (bypass privacy settings)
        $profileUser->load([
            'galleryImages' => function ($query) {
                $query->active()->ordered();
            }
        ]);

        return view('sugar-partner.exchange.profile', compact(
            'exchange',
            'user',
            'profileUser'
        ));
    }

    /**
     * Submit response to exchange.
     */
    public function submitResponse(Request $request, SugarPartnerExchange $exchange)
    {
        $user = Auth::user();

        // Verify user is part of this exchange
        if ($exchange->user1_id !== $user->id && $exchange->user2_id !== $user->id) {
            abort(403, 'You are not authorized to respond to this exchange.');
        }

        // Verify both users have paid
        if (!$exchange->bothUsersPaid()) {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'Both users must complete payment before responding.');
        }

        // Check if user has already responded
        if ($exchange->getUserResponse($user->id)) {
            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('error', 'You have already responded to this exchange.');
        }

        $validator = Validator::make($request->all(), [
            'rejection_type' => 'required|in:accept,soft_reject,hard_reject',
            'rejection_reason' => 'nullable|string|max:1000',
            'admin_note' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::beginTransaction();
        try {
            SugarPartnerRejection::createRejection(
                $exchange,
                $user->id,
                $request->rejection_type,
                $request->rejection_reason,
                $request->admin_note
            );

            DB::commit();

            $responseType = match($request->rejection_type) {
                'accept' => 'acceptance',
                'soft_reject' => 'soft rejection',
                'hard_reject' => 'hard rejection',
            };

            return redirect()->route('sugar-partner.exchange.status', $exchange)
                ->with('success', "Your {$responseType} has been recorded and the other user has been notified.");

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Failed to submit response: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Process wallet payment.
     */
    protected function processWalletPayment(SugarPartnerExchange $exchange, User $user): void
    {
        $wallet = $user->getWallet();

        if ($wallet->balance < $exchange->exchange_price) {
            throw new \Exception('Insufficient wallet balance.');
        }

        // Deduct from wallet
        $wallet->deductMoney(
            $exchange->exchange_price,
            "Sugar Partner Exchange Payment - Exchange #{$exchange->id}"
        );

        // Create payment record
        $payment = SugarPartnerExchangePayment::createForExchange($exchange, $user->id, [
            'payment_method' => 'wallet',
            'payment_details' => [
                'wallet_balance_before' => $wallet->balance + $exchange->exchange_price,
                'wallet_balance_after' => $wallet->balance,
            ]
        ]);

        $payment->markAsCompleted([
            'payment_source' => 'wallet',
            'transaction_id' => 'wallet_' . time() . '_' . $user->id,
        ]);
    }

    /**
     * Process Razorpay payment.
     */
    protected function processRazorpayPayment(SugarPartnerExchange $exchange, User $user, Request $request): void
    {
        // In a real implementation, you would verify the payment signature here
        // For now, we'll create the payment record

        $payment = SugarPartnerExchangePayment::createForExchange($exchange, $user->id, [
            'payment_method' => 'razorpay',
            'payment_id' => $request->razorpay_payment_id,
            'order_id' => $request->razorpay_order_id,
            'signature' => $request->razorpay_signature,
            'payment_details' => [
                'razorpay_payment_id' => $request->razorpay_payment_id,
                'razorpay_order_id' => $request->razorpay_order_id,
                'razorpay_signature' => $request->razorpay_signature,
            ]
        ]);

        $payment->markAsCompleted([
            'payment_source' => 'razorpay',
            'verified' => true, // In real implementation, verify signature
        ]);
    }
}
