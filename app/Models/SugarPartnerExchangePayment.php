<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SugarPartnerExchangePayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'exchange_id',
        'user_id',
        'amount',
        'currency',
        'payment_method',
        'status',
        'payment_id',
        'order_id',
        'signature',
        'payment_details',
        'paid_at',
        'failure_reason',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_details' => 'array',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the exchange this payment belongs to.
     */
    public function exchange(): BelongsTo
    {
        return $this->belongsTo(SugarPartnerExchange::class, 'exchange_id');
    }

    /**
     * Get the user who made this payment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Mark payment as completed.
     */
    public function markAsCompleted(array $paymentDetails = []): void
    {
        $this->update([
            'status' => 'completed',
            'paid_at' => now(),
            'payment_details' => array_merge($this->payment_details ?? [], $paymentDetails),
        ]);

        // Check if both users have paid and update exchange status
        $this->exchange->markPaymentCompleted();
    }

    /**
     * Mark payment as failed.
     */
    public function markAsFailed(string $reason): void
    {
        $this->update([
            'status' => 'failed',
            'failure_reason' => $reason,
        ]);
    }

    /**
     * Create a payment record for an exchange.
     */
    public static function createForExchange(SugarPartnerExchange $exchange, int $userId, array $paymentData = []): self
    {
        // Get the specific price for this user based on their Sugar Partner type
        $userPrice = $exchange->getPriceForUser($userId);

        return self::create([
            'exchange_id' => $exchange->id,
            'user_id' => $userId,
            'amount' => $userPrice,
            'currency' => $exchange->currency,
            'payment_method' => $paymentData['payment_method'] ?? 'razorpay',
            'payment_id' => $paymentData['payment_id'] ?? null,
            'order_id' => $paymentData['order_id'] ?? null,
            'signature' => $paymentData['signature'] ?? null,
            'payment_details' => $paymentData['payment_details'] ?? [],
        ]);
    }

    /**
     * Process refund for this payment.
     */
    public function processRefund(string $reason = 'Exchange cancelled'): bool
    {
        if ($this->status !== 'completed') {
            return false;
        }

        // Here you would integrate with your payment gateway to process the actual refund
        // For now, we'll just mark it as refunded
        $this->update([
            'status' => 'refunded',
            'failure_reason' => $reason,
        ]);

        return true;
    }

    /**
     * Get payment status badge class for UI.
     */
    public function getStatusBadgeClass(): string
    {
        return match($this->status) {
            'completed' => 'bg-success',
            'pending' => 'bg-warning',
            'failed' => 'bg-danger',
            'refunded' => 'bg-secondary',
            default => 'bg-secondary'
        };
    }

    /**
     * Get payment method display name.
     */
    public function getPaymentMethodDisplayName(): string
    {
        return match($this->payment_method) {
            'razorpay' => 'Razorpay',
            'wallet' => 'Wallet',
            'stripe' => 'Stripe',
            'paypal' => 'PayPal',
            default => ucfirst($this->payment_method)
        };
    }
}
