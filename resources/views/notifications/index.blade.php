<x-app-layout>
    <style>
        /* Star rating styles */
        .star-btn {
            border: none;
            background: none;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }
        .star-btn:hover {
            transform: scale(1.1);
        }
        .star-btn:focus {
            outline: none;
        }

        /* Modal styles */
        #reviewModal, #myReviewsModal {
            backdrop-filter: blur(4px);
        }

        /* My Reviews Modal specific styles */
        #myReviewsModal {
            display: flex !important;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        #myReviewsModal.hidden {
            display: none !important;
        }

        #myReviewsModal .max-w-4xl {
            max-width: 56rem;
            max-height: 90vh;
            width: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        #myReviewsModal .modal-content {
            flex: 1;
            overflow-y: auto;
        }

        @media (max-width: 768px) {
            #myReviewsModal {
                padding: 0.5rem;
            }

            #myReviewsModal .max-w-4xl {
                max-width: 100%;
                max-height: 95vh;
            }
        }

        @media (max-width: 480px) {
            #myReviewsModal {
                padding: 0.25rem;
            }

            #myReviewsModal .max-w-4xl {
                max-height: 98vh;
            }
        }

        /* Review card hover effects */
        .review-card {
            transition: all 0.3s ease;
        }

        .review-card:hover {
            transform: translateY(-1px);
        }

        /* Custom scrollbar for reviews container */
        #myReviewsContainer::-webkit-scrollbar {
            width: 6px;
        }

        #myReviewsContainer::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        #myReviewsContainer::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        #myReviewsContainer::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Rating text styles */
        #ratingText {
            font-weight: 500;
            min-height: 20px;
        }

        /* Notification card hover effects */
        .notification-card {
            transition: all 0.3s ease;
        }
        .notification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>

    <!-- Hero Section -->
    <x-hero-section
        title="Your Notifications"
        subtitle="Stay updated with the latest <span class='font-semibold text-gradient-primary'>news</span> and updates"
        description=""
        :showSteps="false"
    />

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <!-- Active Meetings Section -->
            @php
                $isMeetingVerificationEnabled = \App\Helpers\FeatureHelper::isMeetingVerificationEnabled();
                $activeMeetings = collect();

                if ($isMeetingVerificationEnabled) {
                    $activeMeetings = \App\Models\TimeSpendingBooking::with(['client', 'provider', 'meetingVerification'])
                        ->where(function($query) {
                            $query->where('client_id', Auth::user()->id)
                                  ->orWhere('provider_id', Auth::user()->id);
                        })
                        ->where('provider_status', 'accepted')
                        ->where('payment_status', 'paid')
                        ->where('booking_date', '>=', now()->subHours(1))
                        ->where('booking_date', '<=', now()->addHours(24))
                        ->orderBy('booking_date', 'asc')
                        ->get();
                }
            @endphp

            @if($isMeetingVerificationEnabled && $activeMeetings->count() > 0)
                <div class="mb-8">
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200">
                        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <svg class="w-6 h-6 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Active Meetings
                        </h2>

                        <div class="space-y-4">
                            @foreach($activeMeetings as $meeting)
                                @php
                                    $isClient = Auth::user()->id === $meeting->client_id;
                                    $otherUser = $isClient ? $meeting->provider : $meeting->client;
                                    $verification = $meeting->meetingVerification;
                                    $currentTime = now();
                                    $meetingStart = $meeting->booking_date;
                                    $actualDuration = $meeting->actual_duration_hours ?? $meeting->duration_hours;
                                    $meetingEnd = $meeting->booking_date->copy()->addHours((float) $actualDuration);
                                    $isMeetingTime = $currentTime->between($meetingStart->copy()->subMinutes(15), $meetingEnd->copy()->addMinutes(15));
                                    $hasStarted = $verification && $verification->hasBothStartPhotos();
                                    $hasEnded = $verification && $verification->hasBothEndPhotos();
                                @endphp

                                <div class="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center mb-2">
                                                <img src="{{ $otherUser->profile_picture ? asset('storage/' . $otherUser->profile_picture) : asset('images/default-avatar.png') }}"
                                                     alt="{{ $otherUser->name }}"
                                                     class="w-10 h-10 rounded-full mr-3">
                                                <div>
                                                    <h3 class="font-semibold text-gray-800">{{ $otherUser->name }}</h3>
                                                    <p class="text-sm text-gray-600">
                                                        {{ $meetingStart->format('M d, Y') }} at {{ $meetingStart->format('h:i A') }} - {{ $meetingEnd->format('h:i A') }}
                                                    </p>
                                                </div>
                                            </div>

                                            <div class="flex items-center space-x-4 text-sm">
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    @php
                                                        $actualDuration = $meeting->actual_duration_hours ?? $meeting->duration_hours;
                                                        $hours = floor($actualDuration);
                                                        $minutes = round(($actualDuration - $hours) * 60);
                                                    @endphp
                                                    @if($hours == 0)
                                                        {{ $minutes }} minutes
                                                    @elseif($minutes == 0)
                                                        {{ $hours }} hour{{ $hours > 1 ? 's' : '' }}
                                                    @else
                                                        {{ $hours }} hour{{ $hours > 1 ? 's' : '' }} {{ $minutes }} min
                                                    @endif
                                                </span>
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    {{ Str::limit($meeting->meeting_location, 30) }}
                                                </span>
                                            </div>
                                        </div>

                                        <div class="flex flex-col space-y-2">
                                            @if($isMeetingTime && !$hasEnded)
                                                @if(!$hasStarted)
                                                    <button onclick="startMeeting({{ $meeting->id }})"
                                                            class="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors flex items-center">
                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        Start Meeting
                                                    </button>
                                                @else
                                                    <button onclick="endMeeting({{ $meeting->id }})"
                                                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center">
                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                        </svg>
                                                        End Meeting
                                                    </button>
                                                @endif
                                            @endif

                                            @if($hasEnded)
                                                <div class="px-4 py-2 text-sm font-medium text-green-800 bg-green-100 rounded-lg flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Completed
                                                </div>
                                            @elseif($hasStarted)
                                                <div class="px-4 py-2 text-sm font-medium text-blue-800 bg-blue-100 rounded-lg flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5 9.293 10.793a1 1 0 101.414 1.414l2-2a1 1 0 000-1.414z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    In Progress
                                                </div>
                                            @else
                                                <div class="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg flex items-center">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    Scheduled
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif



            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <!-- Pending Bookings Section -->
                    @php
                        $pendingBookings = \App\Models\TimeSpendingBooking::with(['provider'])
                            ->where('client_id', Auth::user()->id)
                            ->where('provider_status', 'pending')
                            ->where('payment_status', 'paid')
                            ->where('status', '!=', 'cancelled')
                            ->orderBy('booking_date', 'asc')
                            ->get();
                    @endphp

                    @if ($pendingBookings->count() > 0)
                        <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <h3 class="text-lg font-semibold text-yellow-800">Your Pending Bookings</h3>
                                <span class="ml-2 px-2 py-1 text-xs font-medium bg-yellow-200 text-yellow-800 rounded-full">
                                    {{ $pendingBookings->count() }}
                                </span>
                            </div>
                            <p class="text-sm text-yellow-700 mb-4">These bookings are waiting for provider acceptance. You can cancel them if needed.</p>

                            <div class="space-y-3">
                                @foreach ($pendingBookings as $pendingBooking)
                                    <div class="bg-white rounded-lg p-4 border border-yellow-200">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1">
                                                <div class="flex items-center mb-2">
                                                    <h4 class="font-medium text-gray-900">
                                                        Booking with
                                                        <a href="/find-person/{{ $pendingBooking->provider->id }}" class="text-blue-600 hover:text-blue-800 hover:underline">
                                                            {{ $pendingBooking->provider->name }}
                                                        </a>
                                                    </h4>
                                                    <span class="ml-2 px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                                                        Pending
                                                    </span>
                                                </div>

                                                <div class="grid grid-cols-2 gap-3 text-sm text-gray-600 mb-3">
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                        </svg>
                                                        <span>{{ $pendingBooking->booking_date->format('M d, Y') }}</span>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        @php
                                                            $actualDuration = $pendingBooking->actual_duration_hours ?? $pendingBooking->duration_hours;
                                                            $hours = floor($actualDuration);
                                                            $minutes = round(($actualDuration - $hours) * 60);
                                                            $durationText = $hours == 0 ? $minutes . 'm' : ($minutes == 0 ? $hours . 'h' : $hours . 'h ' . $minutes . 'm');
                                                        @endphp
                                                        <span>{{ $pendingBooking->booking_date->format('h:i A') }} ({{ $durationText }})</span>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                        </svg>
                                                        <span class="font-medium">₹{{ number_format($pendingBooking->total_amount) }}</span>
                                                    </div>
                                                    <div class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        </svg>
                                                        <span class="truncate">{{ Str::limit($pendingBooking->meeting_location, 30) }}</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="ml-4 flex space-x-2">
                                                @if ($pendingBooking->provider_status === 'pending' && $pendingBooking->status !== 'cancelled')
                                                    <button onclick="showCancelModal({{ $pendingBooking->id }})"
                                                            class="px-3 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors">
                                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                        </svg>
                                                        Cancel Booking
                                                    </button>

                                                    <button onclick="redirectToUpdateBooking({{ $pendingBooking->id }})"
                                                            class="px-3 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                        Update Booking
                                                    </button>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Your Notifications</h3>

                        @if ($notifications->count() > 0)
                            <form action="{{ route('notifications.mark-all-as-read') }}" method="POST">
                                @csrf
                                <button type="submit" class="px-3 py-1 text-xs font-medium text-indigo-600 hover:text-indigo-900 bg-indigo-50 hover:bg-indigo-100 rounded-md transition-colors">
                                    Mark All Read
                                </button>
                            </form>
                        @endif
                    </div>

                    @if ($notifications->count() > 0)
                        <div class="space-y-2">
                            @foreach ($notifications as $notification)
                                <div class="border rounded-lg p-4 {{ $notification->is_read ? 'bg-white border-gray-200' : 'bg-blue-50 border-blue-200' }}">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center gap-2 mb-1">
                                                @php
                                                    // Get booking data for title generation
                                                    $titleBooking = null;
                                                    if (in_array($notification->type, ['booking_request', 'booking_accepted', 'booking_rejected', 'booking_auto_rejected', 'booking_cancelled', 'booking_updated']) && isset($notification->data['booking_id'])) {
                                                        $titleBooking = \App\Models\TimeSpendingBooking::with(['client', 'provider'])->find($notification->data['booking_id']);
                                                    }
                                                @endphp

                                                <h3 class="text-sm font-semibold text-gray-900 truncate">
                                                    @if ($notification->type === 'booking_request' && $titleBooking)
                                                        @if (isset($notification->data['is_update']) && $notification->data['is_update'])
                                                            Updated Booking Request from <a href="{{ route('find-person.show-from-hire-request', $titleBooking->client->id) }}" class="text-blue-600 hover:text-blue-800 hover:underline">{{ $titleBooking->client->name }}</a>
                                                        @else
                                                            New Booking Request from <a href="{{ route('find-person.show-from-hire-request', $titleBooking->client->id) }}" class="text-blue-600 hover:text-blue-800 hover:underline">{{ $titleBooking->client->name }}</a>
                                                        @endif
                                                    @elseif ($notification->type === 'booking_updated' && $titleBooking)
                                                        Updated Booking Request from <a href="{{ route('find-person.show-from-hire-request', $titleBooking->client->id) }}" class="text-blue-600 hover:text-blue-800 hover:underline">{{ $titleBooking->client->name }}</a>
                                                    @elseif ($notification->type === 'booking_cancelled' && $titleBooking)
                                                        @if (Auth::user()->id === $titleBooking->provider_id)
                                                            Booking Cancelled by <a href="{{ route('find-person.show-from-hire-request', $titleBooking->client->id) }}" class="text-blue-600 hover:text-blue-800 hover:underline">{{ $titleBooking->client->name }}</a>
                                                        @else
                                                            Booking Cancelled by <a href="/find-person/{{ $titleBooking->provider->id }}" class="text-blue-600 hover:text-blue-800 hover:underline">{{ $titleBooking->provider->name }}</a>
                                                        @endif
                                                    @else
                                                        {{ $notification->title }}
                                                    @endif
                                                </h3>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full flex-shrink-0
                                                    @if ($notification->type === 'general') bg-blue-100 text-blue-700
                                                    @elseif ($notification->type === 'payment') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'refund_processed') bg-blue-100 text-blue-700
                                                    @elseif ($notification->type === 'match') bg-purple-100 text-purple-700
                                                    @elseif ($notification->type === 'couple_activity') bg-pink-100 text-pink-700
                                                    @elseif ($notification->type === 'review_reminder') bg-yellow-100 text-yellow-700
                                                    @elseif ($notification->type === 'review_received') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'booking_request')
                                                        @php
                                                            $colorBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id'] ?? null);
                                                        @endphp
                                                        @if ($colorBooking && $colorBooking->provider_status === 'rejected' && $colorBooking->rejection_reason === 'Provider selected another client for this time slot')
                                                            bg-gray-100 text-gray-700
                                                        @else
                                                            bg-orange-100 text-orange-700
                                                        @endif
                                                    @elseif ($notification->type === 'booking_accepted') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'booking_rejected') bg-red-100 text-red-700
                                                    @elseif ($notification->type === 'booking_auto_rejected') bg-gray-100 text-gray-700
                                                    @elseif ($notification->type === 'payment_received') bg-emerald-100 text-emerald-700
                                                    @elseif ($notification->type === 'withdrawal_requested') bg-yellow-100 text-yellow-700
                                                    @elseif ($notification->type === 'withdrawal_completed') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'withdrawal_rejected') bg-red-100 text-red-700
                                                    @elseif ($notification->type === 'withdrawal_cancelled') bg-gray-100 text-gray-700
                                                    @elseif ($notification->type === 'sugar_partner_exchange_initiated') bg-pink-100 text-pink-700
                                                    @elseif ($notification->type === 'sugar_partner_payment_completed') bg-green-100 text-green-700
                                                    @elseif ($notification->type === 'sugar_partner_profiles_viewable') bg-purple-100 text-purple-700
                                                    @elseif ($notification->type === 'sugar_partner_response_received') bg-blue-100 text-blue-700
                                                    @elseif ($notification->type === 'sugar_partner_hard_reject_converted') bg-orange-100 text-orange-700
                                                    @elseif ($notification->type === 'sugar_partner_rejection_cleared') bg-teal-100 text-teal-700
                                                    @else bg-gray-100 text-gray-700
                                                    @endif">
                                                    @if ($notification->type === 'booking_request')
                                                        @php
                                                            $badgeBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id'] ?? null);
                                                        @endphp
                                                        @if ($badgeBooking && $badgeBooking->provider_status === 'accepted')
                                                            Booking Accepted
                                                        @elseif ($badgeBooking && $badgeBooking->provider_status === 'rejected')
                                                            @if ($badgeBooking->rejection_reason === 'Provider selected another client for this time slot')
                                                                Booking Auto-Rejected
                                                            @else
                                                                Booking Rejected
                                                            @endif
                                                        @elseif (isset($notification->data['is_update']) && $notification->data['is_update'])
                                                            Updated Booking Request
                                                        @else
                                                            Booking Request
                                                        @endif
                                                    @elseif ($notification->type === 'booking_auto_rejected')
                                                        Booking Auto-Rejected
                                                    @elseif ($notification->type === 'refund_processed')
                                                        Refund to your wallet
                                                    @elseif ($notification->type === 'meeting_start_reminder')
                                                        Meeting Start - Photo Required
                                                    @elseif ($notification->type === 'meeting_end_reminder')
                                                        Meeting End - Photo Required
                                                    @elseif ($notification->type === 'meeting_completed')
                                                        Meeting Completed
                                                    @elseif ($notification->type === 'review_reminder')
                                                        Review Reminder
                                                    @elseif ($notification->type === 'review_received')
                                                        Review Received
                                                    @elseif ($notification->type === 'withdrawal_requested')
                                                        Withdrawal Request Submitted
                                                    @elseif ($notification->type === 'withdrawal_completed')
                                                        Withdrawal Completed
                                                    @elseif ($notification->type === 'withdrawal_rejected')
                                                        Withdrawal Request Rejected
                                                    @elseif ($notification->type === 'withdrawal_cancelled')
                                                        Withdrawal Cancelled
                                                    @elseif ($notification->type === 'sugar_partner_exchange_initiated')
                                                        Sugar Partner Exchange
                                                    @elseif ($notification->type === 'sugar_partner_payment_completed')
                                                        Payment Successful
                                                    @elseif ($notification->type === 'sugar_partner_profiles_viewable')
                                                        Profiles Ready
                                                    @elseif ($notification->type === 'sugar_partner_response_received')
                                                        Response Received
                                                    @elseif ($notification->type === 'sugar_partner_hard_reject_converted')
                                                        Hard Reject Converted
                                                    @elseif ($notification->type === 'sugar_partner_rejection_cleared')
                                                        Rejection Cleared
                                                    @else
                                                        {{ ucfirst(str_replace('_', ' ', $notification->type)) }}
                                                    @endif
                                                </span>
                                            </div>
                                            @if (in_array($notification->type, ['booking_request', 'booking_accepted', 'booking_rejected', 'booking_auto_rejected', 'booking_cancelled', 'booking_updated', 'payment_received', 'meeting_start_reminder', 'meeting_end_reminder', 'meeting_completed', 'review_reminder', 'review_received']) && isset($notification->data['booking_id']))
                                                @php
                                                    $booking = \App\Models\TimeSpendingBooking::with(['client', 'provider'])->find($notification->data['booking_id']);
                                                @endphp

                                                <!-- Short message with expand button -->
                                                <div class="flex items-start gap-2">
                                                    <p class="text-sm text-gray-700 mb-2 flex-1">
                                                        {{ Str::limit($notification->message ?? $notification->body, 80) }}
                                                    </p>
                                                    <button onclick="toggleDetails('notification-{{ $notification->id }}')"
                                                            class="text-blue-600 hover:text-blue-800 flex-shrink-0 mt-1">
                                                        <svg id="arrow-notification-{{ $notification->id }}" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                    </button>
                                                </div>

                                                <!-- Full details (hidden by default) -->
                                                <div id="details-notification-{{ $notification->id }}" class="hidden mt-2">
                                                    @if ($booking)
                                                        <!-- Enhanced Booking Details -->
                                                        <div class="bg-gray-50 rounded-lg p-3">
                                                            <div class="flex items-start justify-between mb-2">
                                                                <div class="flex-1">
                                                                    @if ($notification->type === 'booking_request')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            <a href="{{ route('find-person.show-from-hire-request', $booking->client->id) }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                {{ $booking->client->name }}
                                                                            </a>
                                                                            @if (isset($notification->data['is_update']) && $notification->data['is_update'])
                                                                                @php
                                                                                    $actualDuration = $booking->actual_duration_hours ?? $booking->duration_hours;
                                                                                    $hours = floor($actualDuration);
                                                                                    $minutes = round(($actualDuration - $hours) * 60);
                                                                                    $durationText = $hours == 0 ? $minutes . ' minutes' : ($minutes == 0 ? $hours . ' hour' . ($hours > 1 ? 's' : '') : $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ' . $minutes . ' min');
                                                                                @endphp
                                                                                has updated their booking request for {{ $durationText }}
                                                                            @else
                                                                                @php
                                                                                    $actualDuration = $booking->actual_duration_hours ?? $booking->duration_hours;
                                                                                    $hours = floor($actualDuration);
                                                                                    $minutes = round(($actualDuration - $hours) * 60);
                                                                                    $durationText = $hours == 0 ? $minutes . ' minutes' : ($minutes == 0 ? $hours . ' hour' . ($hours > 1 ? 's' : '') : $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ' . $minutes . ' min');
                                                                                @endphp
                                                                                wants to hire you for {{ $durationText }}
                                                                            @endif
                                                                        </p>
                                                                    @elseif ($notification->type === 'booking_accepted')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            <a href="/find-person/{{ $booking->provider->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                {{ $booking->provider->name }}
                                                                            </a>
                                                                            has accepted your booking request
                                                                        </p>
                                                                    @elseif ($notification->type === 'booking_rejected')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            <a href="/find-person/{{ $booking->provider->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                {{ $booking->provider->name }}
                                                                            </a>
                                                                            has rejected your booking request
                                                                        </p>
                                                                        @if(isset($notification->data['reason']) && $notification->data['reason'])
                                                                            <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                                                                                <p class="text-sm text-red-800">
                                                                                    <span class="font-medium">Reason:</span> {{ $notification->data['reason'] }}
                                                                                </p>
                                                                            </div>
                                                                        @endif
                                                                    @elseif ($notification->type === 'payment_received')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            Payment received from
                                                                            <a href="/find-person/{{ $booking->client->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                {{ $booking->client->name }}
                                                                            </a>
                                                                        </p>
                                                                    @elseif ($notification->type === 'booking_cancelled')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            @if (Auth::user()->id === $booking->provider_id)
                                                                                <a href="/find-person/{{ $booking->client->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                    {{ $booking->client->name }}
                                                                                </a>
                                                                                has cancelled their booking request
                                                                            @else
                                                                                Your booking with
                                                                                <a href="/find-person/{{ $booking->provider->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                    {{ $booking->provider->name }}
                                                                                </a>
                                                                                has been cancelled
                                                                            @endif
                                                                        </p>
                                                                    @elseif ($notification->type === 'booking_auto_rejected')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            Your booking request with
                                                                            <a href="/find-person/{{ $booking->provider->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                {{ $booking->provider->name }}
                                                                            </a>
                                                                            was automatically rejected because they accepted another request for the same time slot
                                                                        </p>
                                                                    @elseif ($notification->type === 'booking_updated')
                                                                        <p class="text-sm text-gray-800 mb-2">
                                                                            @if (Auth::user()->id === $booking->provider_id)
                                                                                <a href="/find-person/{{ $booking->client->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                    {{ $booking->client->name }}
                                                                                </a>
                                                                                has updated their booking request
                                                                            @else
                                                                                You have updated your booking with
                                                                                <a href="/find-person/{{ $booking->provider->id }}" class="font-medium text-blue-600 hover:text-blue-800 hover:underline">
                                                                                    {{ $booking->provider->name }}
                                                                                </a>
                                                                            @endif
                                                                        </p>
                                                                    @endif

                                                                    <!-- Booking Details Grid -->
                                                                    <div class="grid grid-cols-2 gap-3 text-xs">
                                                                        <div class="flex items-center">
                                                                            <svg class="w-3 h-3 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                            </svg>
                                                                            <span class="text-gray-600">{{ $booking->booking_date->format('M d, Y') }}</span>
                                                                        </div>
                                                                        <div class="flex items-center">
                                                                            <svg class="w-3 h-3 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                            </svg>
                                                                            <span class="text-gray-600">
                                                                                {{ $booking->booking_date->format('h:i A') }} -
                                                                                @php $actualDuration = $booking->actual_duration_hours ?? $booking->duration_hours; @endphp
                                                                                {{ $booking->booking_date->copy()->addHours((float) $actualDuration)->format('h:i A') }}
                                                                            </span>
                                                                        </div>
                                                                        <div class="flex items-center">
                                                                            <svg class="w-3 h-3 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                            </svg>
                                                                            @php
                                                                                $actualDuration = $booking->actual_duration_hours ?? $booking->duration_hours;
                                                                                $hours = floor($actualDuration);
                                                                                $minutes = round(($actualDuration - $hours) * 60);
                                                                                $durationText = $hours == 0 ? $minutes . ' minutes' : ($minutes == 0 ? $hours . ' hour' . ($hours > 1 ? 's' : '') : $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ' . $minutes . ' min');
                                                                            @endphp
                                                                            <span class="text-gray-600">{{ $durationText }}</span>
                                                                        </div>
                                                                        <div class="flex items-center">
                                                                            <svg class="w-3 h-3 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                                            </svg>
                                                                            <span class="text-gray-600">
                                                                                @if ($notification->type === 'payment_received')
                                                                                    ₹{{ number_format($notification->data['amount'] ?? $booking->total_amount, 0) }}
                                                                                @else
                                                                                    ₹{{ number_format($booking->total_amount, 0) }}
                                                                                @endif
                                                                            </span>
                                                                        </div>
                                                                        @if ($booking->meeting_location)
                                                                            <div class="flex items-center col-span-2">
                                                                                <svg class="w-3 h-3 text-gray-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                                </svg>
                                                                                <span class="text-gray-600">{{ Str::limit($booking->meeting_location, 40) }}</span>
                                                                            </div>
                                                                        @endif
                                                                        @if ($booking->notes)
                                                                            <div class="col-span-2 mt-2 p-2 bg-blue-50 rounded border-l-2 border-blue-200">
                                                                                <div class="flex items-start">
                                                                                    <svg class="w-3 h-3 text-blue-500 mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                                                                                    </svg>
                                                                                    <div>
                                                                                        <p class="text-xs font-medium text-blue-700 mb-1">Note:</p>
                                                                                        <p class="text-xs text-blue-600">{{ $booking->notes }}</p>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Meeting Verification Section -->
                                                        @if ($isMeetingVerificationEnabled && in_array($notification->type, ['booking_accepted', 'meeting_start_reminder', 'meeting_end_reminder', 'meeting_completed']) && $booking && $booking->provider_status === 'accepted')
                                                            @php
                                                                $verification = $booking->meetingVerification;
                                                                $currentTime = now();
                                                                $bookingStartTime = $booking->booking_date;
                                                                $actualDuration = $booking->actual_duration_hours ?? $booking->duration_hours;
                                                                $bookingEndTime = $booking->booking_date->copy()->addHours((float) $actualDuration);
                                                                $isWithinMeetingTime = $currentTime->between($bookingStartTime->copy()->subMinutes(15), $bookingEndTime->copy()->addMinutes(15));
                                                                $isMeetingTime = $currentTime->between($bookingStartTime, $bookingEndTime);
                                                                $isMeetingStarted = $verification && $verification->hasBothStartPhotos();
                                                                $isMeetingEnded = $verification && $verification->hasBothEndPhotos();
                                                                $userIsClient = Auth::user()->id === $booking->client_id;
                                                            @endphp

                                                            @if ($isWithinMeetingTime || $verification)
                                                                <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                                                    <h4 class="text-sm font-semibold text-blue-800 mb-3 flex items-center">
                                                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                        </svg>
                                                                        Meeting Verification
                                                                    </h4>

                                                                    @if (!$verification)
                                                                        <p class="text-sm text-blue-700 mb-3">
                                                                            When your meeting starts, both you and {{ $userIsClient ? $booking->provider->name : $booking->client->name }} will need to take photos using your device camera to verify the meeting.
                                                                        </p>
                                                                    @else
                                                                        @php
                                                                            $verificationStatus = $verification->getUserVerificationStatus(Auth::user()->id);
                                                                        @endphp

                                                                        <div class="space-y-3">
                                                                            <!-- Meeting Start Status -->
                                                                            <div class="flex items-center justify-between p-3 bg-white rounded border">
                                                                                <div class="flex items-center">
                                                                                    <div class="w-6 h-6 rounded-full mr-3 flex items-center justify-center {{ $verificationStatus['start_photo_uploaded'] ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400' }}">
                                                                                        @if ($verificationStatus['start_photo_uploaded'])
                                                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                                                            </svg>
                                                                                        @else
                                                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"></path>
                                                                                            </svg>
                                                                                        @endif
                                                                                    </div>
                                                                                    <div>
                                                                                        <p class="text-sm font-medium text-gray-900">Your Start Photo</p>
                                                                                        <p class="text-xs text-gray-500">
                                                                                            {{ $verificationStatus['start_photo_uploaded'] ? 'Uploaded' : 'Required when meeting starts' }}
                                                                                        </p>
                                                                                    </div>
                                                                                </div>
                                                                                @if ($isMeetingTime && !$verificationStatus['start_photo_uploaded'])
                                                                                    <button onclick="openCameraModal('start', {{ $booking->id }})"
                                                                                            class="px-3 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                                                                                        Take Photo
                                                                                    </button>
                                                                                @endif
                                                                            </div>

                                                                            <!-- Partner Start Status -->
                                                                            <div class="flex items-center p-3 bg-white rounded border">
                                                                                <div class="w-6 h-6 rounded-full mr-3 flex items-center justify-center {{ $verificationStatus['partner_start_photo_uploaded'] ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400' }}">
                                                                                    @if ($verificationStatus['partner_start_photo_uploaded'])
                                                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                                                        </svg>
                                                                                    @else
                                                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"></path>
                                                                                        </svg>
                                                                                    @endif
                                                                                </div>
                                                                                <div>
                                                                                    <p class="text-sm font-medium text-gray-900">{{ $userIsClient ? $booking->provider->name : $booking->client->name }}'s Start Photo</p>
                                                                                    <p class="text-xs text-gray-500">
                                                                                        {{ $verificationStatus['partner_start_photo_uploaded'] ? 'Uploaded' : 'Waiting for upload' }}
                                                                                    </p>
                                                                                </div>
                                                                            </div>

                                                                            @if ($verificationStatus['meeting_started'])
                                                                                <!-- Meeting End Status -->
                                                                                <div class="flex items-center justify-between p-3 bg-white rounded border">
                                                                                    <div class="flex items-center">
                                                                                        <div class="w-6 h-6 rounded-full mr-3 flex items-center justify-center {{ $verificationStatus['end_photo_uploaded'] ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400' }}">
                                                                                            @if ($verificationStatus['end_photo_uploaded'])
                                                                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                                                                </svg>
                                                                                            @else
                                                                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"></path>
                                                                                                </svg>
                                                                                            @endif
                                                                                        </div>
                                                                                        <div>
                                                                                            <p class="text-sm font-medium text-gray-900">Your End Photo</p>
                                                                                            <p class="text-xs text-gray-500">
                                                                                                {{ $verificationStatus['end_photo_uploaded'] ? 'Uploaded' : 'Required when meeting ends' }}
                                                                                            </p>
                                                                                        </div>
                                                                                    </div>
                                                                                    @if (!$verificationStatus['end_photo_uploaded'])
                                                                                        <button onclick="openCameraModal('end', {{ $booking->id }})"
                                                                                                class="px-3 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors">
                                                                                            Take Photo
                                                                                        </button>
                                                                                    @endif
                                                                                </div>

                                                                                <!-- Partner End Status -->
                                                                                <div class="flex items-center p-3 bg-white rounded border">
                                                                                    <div class="w-6 h-6 rounded-full mr-3 flex items-center justify-center {{ $verificationStatus['partner_end_photo_uploaded'] ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400' }}">
                                                                                        @if ($verificationStatus['partner_end_photo_uploaded'])
                                                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                                                            </svg>
                                                                                        @else
                                                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"></path>
                                                                                            </svg>
                                                                                        @endif
                                                                                    </div>
                                                                                    <div>
                                                                                        <p class="text-sm font-medium text-gray-900">{{ $userIsClient ? $booking->provider->name : $booking->client->name }}'s End Photo</p>
                                                                                        <p class="text-xs text-gray-500">
                                                                                            {{ $verificationStatus['partner_end_photo_uploaded'] ? 'Uploaded' : 'Waiting for upload' }}
                                                                                        </p>
                                                                                    </div>
                                                                                </div>
                                                                            @endif

                                                                            @if ($verificationStatus['is_verified'])
                                                                                <div class="p-3 bg-green-50 border border-green-200 rounded">
                                                                                    <div class="flex items-center">
                                                                                        <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                                                        </svg>
                                                                                        <div>
                                                                                            <p class="text-sm font-semibold text-green-800">Meeting Verified!</p>
                                                                                            <p class="text-xs text-green-700">Duration: {{ $verificationStatus['duration'] }}</p>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            @endif
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            @endif
                                                        @endif
                                                    @endif
                                                </div>
                                            @elseif ($notification->type === 'refund_processed')
                                                <!-- Short message with expand button -->
                                                <div class="flex items-start gap-2">
                                                    <p class="text-sm text-gray-700 mb-2 flex-1">
                                                        {{ Str::limit($notification->message ?? $notification->body, 80) }}
                                                    </p>
                                                    <button onclick="toggleDetails('notification-{{ $notification->id }}')"
                                                            class="text-blue-600 hover:text-blue-800 flex-shrink-0 mt-1">
                                                        <svg id="arrow-notification-{{ $notification->id }}" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                    </button>
                                                </div>

                                                <!-- Full details (hidden by default) -->
                                                <div id="details-notification-{{ $notification->id }}" class="hidden mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                                    <p class="text-sm text-gray-700 mb-3">
                                                        {{ $notification->body ?? $notification->message }}
                                                    </p>
                                                    <div class="bg-white rounded-lg p-3">
                                                        <div class="flex items-center mb-2">
                                                            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                            </svg>
                                                            <span class="font-medium text-blue-800">Refund Details</span>
                                                        </div>
                                                        <p class="text-sm text-blue-700 mb-1">
                                                            Amount refunded: ₹{{ $notification->data['refund_amount'] ?? 'N/A' }}
                                                        </p>
                                                        <p class="text-xs text-blue-600">
                                                            Current wallet balance: ₹{{ $notification->data['wallet_balance'] ?? 'N/A' }}
                                                        </p>
                                                    </div>
                                                </div>
                                            @elseif (in_array($notification->type, ['withdrawal_requested', 'withdrawal_completed', 'withdrawal_cancelled']))
                                                <!-- Simple Withdrawal Notifications (no expand) -->
                                                @php
                                                    // Determine payment method type
                                                    $paymentMethod = 'Bank';
                                                    if (isset($notification->data['bank_account']) && str_contains(strtolower($notification->data['bank_account']), 'gpay')) {
                                                        $paymentMethod = 'G-Pay';
                                                    } elseif (isset($notification->data['bank_account']) && str_contains(strtolower($notification->data['bank_account']), 'g-pay')) {
                                                        $paymentMethod = 'G-Pay';
                                                    }

                                                    // Create simplified message
                                                    $amount = isset($notification->data['amount']) ? '₹' . number_format($notification->data['amount'], 0) : '';

                                                    if ($notification->type === 'withdrawal_requested') {
                                                        $simpleMessage = "Your withdrawal request of {$amount} via {$paymentMethod} has been submitted to admin";
                                                    } elseif ($notification->type === 'withdrawal_completed') {
                                                        $simpleMessage = "Your withdrawal of {$amount} via {$paymentMethod} has been completed successfully";
                                                    } elseif ($notification->type === 'withdrawal_cancelled') {
                                                        $simpleMessage = "Your withdrawal request of {$amount} via {$paymentMethod} has been cancelled";
                                                    } else {
                                                        $simpleMessage = $notification->message ?? $notification->body;
                                                    }
                                                @endphp
                                                <p class="text-sm text-gray-700 mb-2">
                                                    {{ $simpleMessage }}
                                                </p>
                                            @elseif ($notification->type === 'withdrawal_rejected')
                                                <!-- Withdrawal Rejection with Reason -->
                                                <div class="flex items-start gap-2">
                                                    @php
                                                        $paymentMethod = 'Bank';
                                                        if (isset($notification->data['bank_account']) && str_contains(strtolower($notification->data['bank_account']), 'gpay')) {
                                                            $paymentMethod = 'G-Pay';
                                                        } elseif (isset($notification->data['bank_account']) && str_contains(strtolower($notification->data['bank_account']), 'g-pay')) {
                                                            $paymentMethod = 'G-Pay';
                                                        }

                                                        $amount = isset($notification->data['amount']) ? '₹' . number_format($notification->data['amount'], 0) : '';
                                                        $simpleMessage = "Your withdrawal request of {$amount} via {$paymentMethod} has been rejected";
                                                    @endphp
                                                    <p class="text-sm text-gray-700 mb-2 flex-1">
                                                        {{ $simpleMessage }}
                                                    </p>
                                                    @if (isset($notification->data['failure_reason']))
                                                        <button onclick="toggleDetails('notification-{{ $notification->id }}')"
                                                                class="text-blue-600 hover:text-blue-800 flex-shrink-0 mt-1">
                                                            <svg id="arrow-notification-{{ $notification->id }}" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                            </svg>
                                                        </button>
                                                    @endif
                                                </div>

                                                @if (isset($notification->data['failure_reason']))
                                                    <!-- Rejection Reason (hidden by default) -->
                                                    <div id="details-notification-{{ $notification->id }}" class="hidden mt-2">
                                                        <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                                                            <div class="flex items-start">
                                                                <svg class="w-4 h-4 text-red-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                                </svg>
                                                                <div>
                                                                    <p class="text-sm font-medium text-red-800 mb-1">Rejection Reason:</p>
                                                                    <p class="text-sm text-red-700">{{ $notification->data['failure_reason'] }}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            @else
                                                <!-- Short message with expand button -->
                                                <div class="flex items-start gap-2">
                                                    <p class="text-sm text-gray-700 mb-2 flex-1">
                                                        {{ Str::limit($notification->message ?? $notification->body, 80) }}
                                                    </p>
                                                    @if (strlen($notification->message ?? $notification->body) > 80)
                                                        <button onclick="toggleDetails('notification-{{ $notification->id }}')"
                                                                class="text-blue-600 hover:text-blue-800 flex-shrink-0 mt-1">
                                                            <svg id="arrow-notification-{{ $notification->id }}" class="w-4 h-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                            </svg>
                                                        </button>
                                                    @endif
                                                </div>

                                                <!-- Full details (hidden by default) -->
                                                @if (strlen($notification->message ?? $notification->body) > 80)
                                                    <div id="details-notification-{{ $notification->id }}" class="hidden mt-2 p-3 bg-gray-50 rounded-lg">
                                                        <p class="text-sm text-gray-700">
                                                            {{ $notification->body ?? $notification->message }}
                                                        </p>
                                                    </div>
                                                @endif
                                            @endif

                                            <p class="text-xs text-gray-500">
                                                {{ $notification->created_at->format('M d, Y H:i') }}
                                            </p>
                                        </div>

                                        <div class="ml-3 flex flex-col space-y-2">
                                            @if (!$notification->is_read)
                                                <form action="{{ route('notifications.mark-as-read', $notification) }}" method="POST">
                                                    @csrf
                                                    <button type="submit" class="text-xs text-indigo-600 hover:text-indigo-900 font-medium">
                                                        Mark Read
                                                    </button>
                                                </form>
                                            @endif

                                            @if ($notification->type === 'booking_request' && isset($notification->data['booking_id']))
                                                @php
                                                    $actionBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $isBlocked = $actionBooking ? \App\Models\BlockedUser::isBlocked(Auth::user()->id, $actionBooking->client_id) : false;
                                                @endphp

                                                @if ($actionBooking && $actionBooking->provider_status === 'pending' && !$isBlocked && Auth::user()->id === $actionBooking->provider_id)
                                                    <!-- Pending booking - show action buttons -->
                                                    <div class="flex space-x-2">
                                                        <button onclick="acceptBooking({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors"
                                                                style="background-color: #059669; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#047857'"
                                                                onmouseout="this.style.backgroundColor='#059669'">
                                                            Accept
                                                        </button>
                                                        <button onclick="showRejectModal({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
                                                                style="background-color: #dc2626; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#b91c1c'"
                                                                onmouseout="this.style.backgroundColor='#dc2626'">
                                                            Reject
                                                        </button>
                                                        <button onclick="blockClient({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-gray-600 hover:bg-gray-700 rounded-md transition-colors"
                                                                style="background-color: #4b5563; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#374151'"
                                                                onmouseout="this.style.backgroundColor='#4b5563'">
                                                            Block
                                                        </button>
                                                    </div>
                                                @elseif ($actionBooking && $actionBooking->provider_status === 'accepted' && Auth::user()->id === $actionBooking->provider_id)
                                                    <!-- Accepted booking - show status -->
                                                    <div class="mt-2 p-2 bg-green-50 rounded-lg border border-green-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-green-800">Booking Accepted</span>
                                                        </div>
                                                    </div>
                                                @elseif ($actionBooking && $actionBooking->provider_status === 'rejected' && $isBlocked && Auth::user()->id === $actionBooking->provider_id)
                                                    <!-- Blocked user - show unblock option -->
                                                    <div class="mt-2 p-2 bg-gray-50 rounded-lg border border-gray-200">
                                                        <div class="flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <svg class="w-4 h-4 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                                </svg>
                                                                <span class="text-sm font-medium text-gray-800">User Blocked</span>
                                                            </div>
                                                            <button onclick="unblockUser({{ $actionBooking->client_id }})"
                                                                    class="px-3 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                                                                    style="background-color: #2563eb; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                    onmouseover="this.style.backgroundColor='#1d4ed8'"
                                                                    onmouseout="this.style.backgroundColor='#2563eb'">
                                                                Unblock
                                                            </button>
                                                        </div>
                                                    </div>
                                                @elseif ($actionBooking && $actionBooking->provider_status === 'rejected' && Auth::user()->id === $actionBooking->provider_id)
                                                    <!-- Rejected booking - show status -->
                                                    <div class="mt-2 p-2 bg-red-50 rounded-lg border border-red-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-red-800">Booking Rejected</span>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif

                                            @if (in_array($notification->type, ['booking_accepted', 'booking_request', 'payment_received']) && isset($notification->data['booking_id']))
                                                @php
                                                    $chatBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    if ($chatBooking) {
                                                        $bookingEndTime = \Carbon\Carbon::parse($chatBooking->booking_date)->addHours((float) $chatBooking->duration_hours);
                                                        $isMeetingCompleted = $chatBooking->isMeetingCompleted() || $bookingEndTime->isPast();
                                                        $isBookingValid = $chatBooking->payment_status === 'paid' && $chatBooking->provider_status === 'accepted';

                                                        $showChatButton = $isBookingValid && !$isMeetingCompleted && $bookingEndTime->isFuture();
                                                        $showReviewButton = $isBookingValid && $isMeetingCompleted && \App\Helpers\FeatureHelper::isRatingReviewSystemActive() && \App\Models\RatingReview::canReviewBooking($chatBooking->id, Auth::user()->id);

                                                        // Get other user name for review
                                                        $otherUser = $chatBooking->client_id === Auth::user()->id ? $chatBooking->provider : $chatBooking->client;
                                                        $otherUserName = $otherUser ? $otherUser->name : 'Unknown User';
                                                    } else {
                                                        $showChatButton = false;
                                                        $showReviewButton = false;
                                                        $otherUserName = '';
                                                    }
                                                @endphp

                                                @if ($showChatButton)
                                                    <div class="flex space-x-2">
                                                        <button onclick="openChat({{ $notification->data['booking_id'] }})"
                                                                class="px-4 py-2 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                                                                style="background-color: #2563eb; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#1d4ed8'"
                                                                onmouseout="this.style.backgroundColor='#2563eb'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                                            </svg>
                                                            Chat Now
                                                        </button>
                                                    </div>
                                                @elseif ($showReviewButton)
                                                    <div class="flex space-x-2">
                                                        <button onclick="openReviewModal({{ $notification->data['booking_id'] }}, '{{ addslashes($otherUserName) }}')"
                                                                class="px-4 py-2 text-xs font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors"
                                                                style="background-color: #d97706; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#b45309'"
                                                                onmouseout="this.style.backgroundColor='#d97706'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                            </svg>
                                                            Rate & Review
                                                        </button>
                                                    </div>
                                                @endif
                                            @endif

                                            <!-- Cancel and Update Booking Buttons for Pending Client Bookings -->
                                            @if (isset($notification->data['booking_id']))
                                                @php
                                                    $actionBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $showButtons = $actionBooking &&
                                                                  $actionBooking->client_id === Auth::user()->id &&
                                                                  $actionBooking->provider_status === 'pending' &&
                                                                  $actionBooking->payment_status === 'paid' &&
                                                                  $actionBooking->status !== 'cancelled';
                                                @endphp

                                                @if ($showButtons)
                                                    <div class="mt-2 flex space-x-2">
                                                        <button onclick="showCancelModal({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
                                                                style="background-color: #dc2626; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#b91c1c'"
                                                                onmouseout="this.style.backgroundColor='#dc2626'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                            </svg>
                                                            Cancel Booking
                                                        </button>

                                                        <button onclick="redirectToUpdateBooking({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                                                                style="background-color: #2563eb; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#1d4ed8'"
                                                                onmouseout="this.style.backgroundColor='#2563eb'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                            </svg>
                                                            Update Booking
                                                        </button>
                                                    </div>
                                                @endif

                                            <!-- Dispute/No-Show Button for Eligible Bookings -->
                                            @if (isset($notification->data['booking_id']))
                                                @php
                                                    $disputeBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $canRaiseDispute = $disputeBooking &&
                                                                      $disputeBooking->client_id === Auth::user()->id &&
                                                                      $disputeBooking->canRaiseNoShowDispute();
                                                    $hasDispute = $disputeBooking && $disputeBooking->escrow_status === 'disputed';
                                                @endphp

                                                @if ($canRaiseDispute)
                                                    <div class="mt-2">
                                                        <button onclick="showDisputeModal({{ $notification->data['booking_id'] }})"
                                                                class="px-3 py-1 text-xs font-medium text-white bg-orange-600 hover:bg-orange-700 rounded-md transition-colors"
                                                                style="background-color: #ea580c; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#c2410c'"
                                                                onmouseout="this.style.backgroundColor='#ea580c'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                            </svg>
                                                            Report No-Show
                                                        </button>
                                                    </div>
                                                @elseif ($hasDispute)
                                                    <div class="mt-2 p-2 bg-orange-50 rounded-lg border border-orange-200">
                                                        <div class="flex items-center justify-between">
                                                            <div class="flex items-center">
                                                                <svg class="w-4 h-4 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                                </svg>
                                                                <span class="text-sm font-medium text-orange-800">{{ $disputeBooking->dispute_status_display }}</span>
                                                            </div>
                                                            <button onclick="viewDisputeDetails({{ $notification->data['booking_id'] }})"
                                                                    class="text-xs text-orange-600 hover:text-orange-800 underline">
                                                                View Details
                                                            </button>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif
                                            @endif

                                            <!-- Review Action Buttons -->
                                            @if ($notification->type === 'review_reminder' && isset($notification->data['booking_id']))
                                                @php
                                                    $reviewBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $canReview = $reviewBooking && \App\Models\RatingReview::canReviewBooking($reviewBooking->id, Auth::user()->id);
                                                @endphp

                                                @if ($canReview)
                                                    <div class="mt-2">
                                                        <button onclick="openReviewModal({{ $notification->data['booking_id'] }}, '{{ addslashes($notification->data['other_user_name'] ?? '') }}')"
                                                                class="px-4 py-2 text-xs font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors"
                                                                style="background-color: #d97706; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                onmouseover="this.style.backgroundColor='#b45309'"
                                                                onmouseout="this.style.backgroundColor='#d97706'">
                                                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                                            </svg>
                                                            Rate & Review
                                                        </button>
                                                    </div>
                                                @else
                                                    <div class="mt-2 p-2 bg-gray-50 rounded-lg border border-gray-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-gray-800">Already Reviewed</span>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif

                                            @if ($notification->type === 'review_received' && isset($notification->data['review_id']))
                                                <div class="mt-2">
                                                    <button onclick="openMyReviewsModal()"
                                                            class="px-4 py-2 text-xs font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors"
                                                            style="background-color: #059669; color: white; padding: 6px 16px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                            onmouseover="this.style.backgroundColor='#047857'"
                                                            onmouseout="this.style.backgroundColor='#059669'">
                                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                        </svg>
                                                        View My Reviews
                                                    </button>
                                                </div>
                                            @endif

                                            <!-- No-Show Detection Notification -->
                                            @if ($notification->type === 'no_show_detected' && isset($notification->data['booking_id']))
                                                @php
                                                    $noShowBooking = \App\Models\TimeSpendingBooking::find($notification->data['booking_id']);
                                                    $canStillDispute = $noShowBooking && $noShowBooking->canRaiseNoShowDispute();
                                                @endphp

                                                @if ($canStillDispute)
                                                    <div class="mt-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                                                        <div class="flex items-start space-x-3">
                                                            <svg class="w-5 h-5 text-orange-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                            </svg>
                                                            <div class="flex-1">
                                                                <p class="text-sm text-orange-800 mb-2">
                                                                    <strong>Potential No-Show Detected:</strong> If {{ $notification->data['provider_name'] ?? 'the provider' }} didn't show up for your meeting, you can report this incident.
                                                                </p>
                                                                <button onclick="showDisputeModal({{ $notification->data['booking_id'] }})"
                                                                        class="px-3 py-1 text-xs font-medium text-white bg-orange-600 hover:bg-orange-700 rounded-md transition-colors"
                                                                        style="background-color: #ea580c; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                        onmouseover="this.style.backgroundColor='#c2410c'"
                                                                        onmouseout="this.style.backgroundColor='#ea580c'">
                                                                    Report No-Show
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="mt-2 p-2 bg-gray-50 rounded-lg border border-gray-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-gray-800">Issue Already Reported or Resolved</span>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif

                                            <!-- Dispute Status Notifications -->
                                            @if (in_array($notification->type, ['dispute_submitted', 'dispute_updated', 'dispute_resolved']) && isset($notification->data['booking_id']))
                                                <div class="mt-2">
                                                    <button onclick="viewDisputeDetails({{ $notification->data['booking_id'] }})"
                                                            class="px-3 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 underline">
                                                        View Dispute Details
                                                    </button>
                                                </div>
                                            @endif

                                            <!-- Couple Activity Action Buttons -->
                                            @if ($notification->type === 'couple_activity' && isset($notification->data['request_id']))
                                                @php
                                                    $coupleRequest = \App\Models\CoupleActivityRequest::find($notification->data['request_id']);
                                                @endphp

                                                @if ($coupleRequest && $coupleRequest->status === 'pending' && $coupleRequest->partner_id === Auth::user()->id)
                                                    <!-- Pending couple activity request - show action buttons -->
                                                    <div class="flex space-x-2 mt-2">
                                                        <form action="{{ route('couple-activity.approve', $coupleRequest) }}" method="POST" class="inline">
                                                            @csrf
                                                            <button type="submit"
                                                                    class="px-3 py-1 text-xs font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors"
                                                                    style="background-color: #059669; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                    onmouseover="this.style.backgroundColor='#047857'"
                                                                    onmouseout="this.style.backgroundColor='#059669'">
                                                                ✓ Accept
                                                            </button>
                                                        </form>
                                                        <form action="{{ route('couple-activity.reject', $coupleRequest) }}" method="POST" class="inline">
                                                            @csrf
                                                            <button type="submit"
                                                                    class="px-3 py-1 text-xs font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors"
                                                                    style="background-color: #d97706; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                    onmouseover="this.style.backgroundColor='#b45309'"
                                                                    onmouseout="this.style.backgroundColor='#d97706'">
                                                                ⚠ Decline
                                                            </button>
                                                        </form>
                                                        <form action="{{ route('couple-activity.block', $coupleRequest) }}" method="POST" class="inline">
                                                            @csrf
                                                            <button type="submit"
                                                                    class="px-3 py-1 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
                                                                    style="background-color: #dc2626; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; border: none; cursor: pointer; transition: all 0.2s;"
                                                                    onmouseover="this.style.backgroundColor='#b91c1c'"
                                                                    onmouseout="this.style.backgroundColor='#dc2626'"
                                                                    onclick="return confirm('Are you sure you want to block this user?')">
                                                                🚫 Block
                                                            </button>
                                                        </form>
                                                    </div>
                                                @elseif ($coupleRequest && $coupleRequest->status === 'approved')
                                                    <!-- Approved couple activity request - show status -->
                                                    <div class="mt-2 p-2 bg-green-50 rounded-lg border border-green-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-green-800">Request Accepted</span>
                                                        </div>
                                                    </div>
                                                @elseif ($coupleRequest && $coupleRequest->status === 'rejected')
                                                    <!-- Rejected couple activity request - show status -->
                                                    <div class="mt-2 p-2 bg-red-50 rounded-lg border border-red-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-red-800">Request Declined</span>
                                                        </div>
                                                    </div>
                                                @elseif ($coupleRequest && $coupleRequest->status === 'blocked')
                                                    <!-- Blocked couple activity request - show status -->
                                                    <div class="mt-2 p-2 bg-gray-50 rounded-lg border border-gray-200">
                                                        <div class="flex items-center">
                                                            <svg class="w-4 h-4 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                            </svg>
                                                            <span class="text-sm font-medium text-gray-800">User Blocked</span>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endif

                                            <!-- Sugar Partner Exchange Action Buttons -->
                                            @if (in_array($notification->type, ['sugar_partner_exchange_initiated', 'sugar_partner_payment_completed', 'sugar_partner_profiles_viewable', 'sugar_partner_response_received', 'sugar_partner_hard_reject_converted', 'sugar_partner_rejection_cleared']) && isset($notification->data['action_url']))
                                                <div class="mt-2">
                                                    <a href="{{ $notification->data['action_url'] }}"
                                                       class="inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-pink-600 hover:bg-pink-700 rounded-md transition-colors"
                                                       style="background-color: #db2777; color: white; padding: 4px 12px; font-size: 12px; font-weight: 500; border-radius: 6px; text-decoration: none; transition: all 0.2s;"
                                                       onmouseover="this.style.backgroundColor='#be185d'"
                                                       onmouseout="this.style.backgroundColor='#db2777'">
                                                        @if ($notification->type === 'sugar_partner_exchange_initiated')
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z"></path>
                                                            </svg>
                                                        @elseif ($notification->type === 'sugar_partner_payment_completed')
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                        @elseif ($notification->type === 'sugar_partner_profiles_viewable')
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                            </svg>
                                                        @else
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                        @endif
                                                        {{ $notification->data['action_text'] ?? 'View Details' }}
                                                    </a>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach

                            <div class="mt-4">
                                {{ $notifications->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
                            <p class="mt-1 text-sm text-gray-500">
                                You don't have any notifications yet.
                            </p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div id="rejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Reject Booking</h3>
                <form id="rejectForm">
                    <div class="mb-4">
                        <label for="rejectionReason" class="block text-sm font-medium text-gray-700 mb-2">
                            Reason for rejection:
                        </label>
                        <textarea id="rejectionReason" name="reason" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                                  placeholder="Please provide a reason for rejecting this booking..."
                                  required></textarea>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeRejectModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors">
                            Reject Booking
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Cancel Booking Modal -->
    <div id="cancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Cancel Booking</h3>
                <div class="mb-4">
                    <p class="text-sm text-gray-700 mb-3">
                        Are you sure you want to cancel this booking? The full amount will be refunded to your wallet.
                    </p>
                    <div id="cancelBookingDetails" class="bg-gray-50 rounded-lg p-3 mb-3">
                        <!-- Booking details will be populated here -->
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeCancelModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                        Keep Booking
                    </button>
                    <button type="button" onclick="confirmCancelBooking()" id="confirmCancelBtn"
                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors">
                        <span id="cancelBtnText">Cancel Booking</span>
                        <span id="cancelBtnLoading" class="hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Cancelling...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Booking Modal -->
    <div id="updateBookingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Update Booking</h3>

                <div id="currentBookingInfo"></div>

                <form class="space-y-4">
                    <div>
                        <label for="updateBookingDate" class="block text-sm font-medium text-gray-700 mb-1">New Date</label>
                        <input type="date" id="updateBookingDate" name="booking_date"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               min="{{ date('Y-m-d') }}" required>
                    </div>

                    <div>
                        <label for="updateBookingTime" class="block text-sm font-medium text-gray-700 mb-1">New Time</label>
                        <input type="time" id="updateBookingTime" name="booking_time"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               required>
                    </div>

                    <div>
                        <label for="updateDurationHours" class="block text-sm font-medium text-gray-700 mb-1">Duration (hours)</label>
                        <select id="updateDurationHours" name="duration_hours"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required>
                            <option value="0.5">30 minutes</option>
                            <option value="1">1 hour</option>
                            <option value="1.5">1.5 hours</option>
                            <option value="2">2 hours</option>
                            <option value="2.5">2.5 hours</option>
                            <option value="3">3 hours</option>
                            <option value="4">4 hours</option>
                            <option value="5">5 hours</option>
                            <option value="6">6 hours</option>
                            <option value="8">8 hours</option>
                            <option value="10">10 hours</option>
                            <option value="12">12 hours</option>
                        </select>
                    </div>

                    <div>
                        <label for="updateLocation" class="block text-sm font-medium text-gray-700 mb-1">Meeting Location</label>
                        <input type="text" id="updateLocation" name="location"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Enter meeting location" required>
                    </div>

                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-600">New Total Amount:</span>
                            <span class="font-semibold text-blue-600">₹<span id="updateTotalAmount">0</span></span>
                        </div>
                        <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                            <span>Duration:</span>
                            <span id="updateTotalHours">0 hours</span>
                        </div>
                    </div>
                </form>

                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeUpdateBookingModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                        Cancel
                    </button>
                    <button type="button" id="updateBookingBtn" onclick="submitUpdateBooking()"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                        <span id="updateBtnText">Update Booking</span>
                        <span id="updateBtnLoading" class="hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Updating...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Camera Modal for Meeting Verification -->
    <div id="cameraModal" class="fixed inset-0 bg-black bg-opacity-80 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="cameraModalTitle" class="text-lg font-medium text-gray-900">Take Meeting Photo</h3>
                    <button onclick="closeCameraModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="cameraInstructions" class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p class="text-sm text-blue-800">
                        <strong>Important:</strong> Camera and location permissions are mandatory for meeting verification. You must use your device camera to take a new photo with precise location data. Gallery uploads are not allowed.
                    </p>
                </div>

                <!-- Location Status -->
                <div id="locationStatus" class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span id="locationText" class="text-sm text-yellow-800">Getting location...</span>
                    </div>
                </div>

                <!-- Camera Preview -->
                <div id="cameraContainer" class="mb-4">
                    <video id="cameraPreview" class="w-full h-64 bg-gray-200 rounded-lg object-cover" autoplay playsinline></video>
                    <canvas id="photoCanvas" class="hidden"></canvas>
                </div>

                <!-- Captured Photo Preview -->
                <div id="photoPreview" class="mb-4 hidden">
                    <img id="capturedPhoto" class="w-full h-64 bg-gray-200 rounded-lg object-cover" alt="Captured photo">
                </div>

                <!-- Camera Controls -->
                <div class="flex justify-center space-x-3">
                    <button id="startCameraBtn" onclick="startCamera()"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                        Start Camera
                    </button>
                    <button id="captureBtn" onclick="capturePhoto()"
                            class="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors hidden">
                        Capture Photo
                    </button>
                    <button id="retakeBtn" onclick="retakePhoto()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors hidden">
                        Retake
                    </button>
                    <button id="uploadBtn" onclick="uploadPhoto()"
                            class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md transition-colors hidden">
                        <span id="uploadBtnText">Upload Photo</span>
                        <span id="uploadBtnLoading" class="hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Uploading...
                        </span>
                    </button>
                </div>

                <div id="cameraError" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg hidden">
                    <p class="text-sm text-red-800"></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Dispute Modal -->
    <div id="disputeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Report Provider No-Show</h3>
                    <button onclick="closeDisputeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <p class="text-sm text-orange-800">
                        <strong>Important:</strong> Only report a no-show if the provider failed to attend the scheduled meeting. False reports may result in account restrictions.
                    </p>
                </div>

                <form id="disputeForm" enctype="multipart/form-data">
                    <input type="hidden" id="disputeBookingId" name="booking_id">
                    <input type="hidden" name="dispute_type" value="no_show">

                    <div class="mb-4">
                        <label for="disputeReason" class="block text-sm font-medium text-gray-700 mb-2">
                            Describe what happened: <span class="text-red-500">*</span>
                        </label>
                        <textarea id="disputeReason" name="reason" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                  placeholder="Please provide details about the no-show incident (minimum 10 characters)..."
                                  required minlength="10" maxlength="1000"></textarea>
                        <div class="text-xs text-gray-500 mt-1">
                            <span id="reasonCharCount">0</span>/1000 characters (minimum 10)
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="evidencePhotos" class="block text-sm font-medium text-gray-700 mb-2">
                            Evidence Photos (Optional)
                        </label>
                        <input type="file" id="evidencePhotos" name="evidence_photos[]"
                               accept="image/jpeg,image/png,image/jpg" multiple
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                        <div class="text-xs text-gray-500 mt-1">
                            You can upload up to 5 photos (max 5MB each). Screenshots of messages, location photos, etc.
                        </div>
                        <div id="photoPreviewContainer" class="mt-2 grid grid-cols-3 gap-2 hidden"></div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeDisputeModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                            Cancel
                        </button>
                        <button type="submit" id="submitDisputeBtn"
                                class="px-4 py-2 text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 rounded-md transition-colors">
                            <span id="disputeBtnText">Submit Report</span>
                            <span id="disputeBtnLoading" class="hidden">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Submitting...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Dispute Details Modal -->
    <div id="disputeDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Dispute Details</h3>
                    <button onclick="closeDisputeDetailsModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="disputeDetailsContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Review Modal -->
    <div id="reviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Rate Your Experience</h3>
                    <button onclick="closeReviewModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="reviewModalContent">
                    <div class="mb-4">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-900" id="reviewUserName">Loading...</p>
                                <p class="text-sm text-gray-600" id="reviewBookingDetails">Loading...</p>
                            </div>
                        </div>
                    </div>

                    <form id="reviewForm" onsubmit="submitReview(event)">
                        <input type="hidden" id="reviewBookingId" name="booking_id">

                        <!-- Rating Section -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-3">
                                How would you rate your experience?
                            </label>
                            <div class="flex justify-center space-x-2 mb-2">
                                <button type="button" class="star-btn text-3xl text-gray-300 hover:text-yellow-400 transition-colors" data-rating="1">★</button>
                                <button type="button" class="star-btn text-3xl text-gray-300 hover:text-yellow-400 transition-colors" data-rating="2">★</button>
                                <button type="button" class="star-btn text-3xl text-gray-300 hover:text-yellow-400 transition-colors" data-rating="3">★</button>
                                <button type="button" class="star-btn text-3xl text-gray-300 hover:text-yellow-400 transition-colors" data-rating="4">★</button>
                                <button type="button" class="star-btn text-3xl text-gray-300 hover:text-yellow-400 transition-colors" data-rating="5">★</button>
                            </div>
                            <p class="text-center text-sm text-gray-500" id="ratingText">Click to rate</p>
                            <input type="hidden" id="selectedRating" name="rating" required>
                        </div>

                        <!-- Review Text -->
                        <div class="mb-4">
                            <label for="reviewText" class="block text-sm font-medium text-gray-700 mb-2">
                                Share your experience (optional)
                            </label>
                            <textarea id="reviewText" name="review_text" rows="4"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                                      placeholder="Tell others about your experience..."></textarea>
                        </div>

                        <!-- Anonymous Option -->
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" id="isAnonymous" name="is_anonymous" class="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500">
                                <span class="ml-2 text-sm text-gray-700">Submit as anonymous review</span>
                            </label>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeReviewModal()"
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                                Cancel
                            </button>
                            <button type="submit" id="submitReviewBtn"
                                    class="px-4 py-2 text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 rounded-md transition-colors">
                                <span id="submitReviewText">Submit Review</span>
                                <span id="submitReviewLoading" class="hidden">
                                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Submitting...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- My Reviews Modal -->
    <div id="myReviewsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50"
         role="dialog" aria-modal="true" aria-labelledby="myReviewsModalTitle">
        <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-xl">
            <div class="p-6">
                <div class="modal-content">
                <div class="flex justify-between items-center mb-6">
                    <h3 id="myReviewsModalTitle" class="text-xl font-semibold text-gray-900">My Reviews</h3>
                    <button onclick="closeMyReviewsModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Reviews Statistics -->
                <div id="reviewsStats" class="mb-6 p-4 bg-gray-50 rounded-lg hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gray-900" id="averageRating">0.0</div>
                            <div class="text-sm text-gray-600 mb-2">Average Rating</div>
                            <div id="averageStars" class="flex justify-center"></div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gray-900" id="totalReviews">0</div>
                            <div class="text-sm text-gray-600">Total Reviews</div>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div id="reviewsLoading" class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
                    <p class="text-gray-600 mt-3 text-sm">Loading your reviews...</p>
                </div>

                <!-- Reviews Container -->
                <div id="myReviewsContainer" class="space-y-4 max-h-80 overflow-y-auto">
                    <!-- Reviews will be loaded here -->
                </div>

                <!-- Empty State -->
                <div id="noReviewsMessage" class="text-center py-8 hidden">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">No Reviews Yet</h4>
                    <p class="text-gray-600">You haven't received any reviews yet. Complete some meetings to start receiving reviews!</p>
                </div>

                <!-- Error State -->
                <div id="reviewsError" class="text-center py-8 hidden">
                    <svg class="w-16 h-16 text-red-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">Failed to Load Reviews</h4>
                    <p class="text-gray-600 mb-4">There was an error loading your reviews. Please try again.</p>
                    <button onclick="loadMyReviews()" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors">
                        Try Again
                    </button>
                </div>

                <!-- Pagination -->
                <div id="reviewsPagination" class="mt-6 flex justify-center hidden">
                    <button id="loadMoreReviews" onclick="loadMoreMyReviews()"
                            class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="loadMoreText">Load More Reviews</span>
                        <span id="loadMoreLoading" class="hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Loading...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle notification details function - defined early to avoid reference errors
        function toggleDetails(notificationId) {
            const detailsDiv = document.getElementById('details-' + notificationId);
            const arrow = document.getElementById('arrow-' + notificationId);

            if (detailsDiv && arrow) {
                if (detailsDiv.classList.contains('hidden')) {
                    detailsDiv.classList.remove('hidden');
                    arrow.style.transform = 'rotate(180deg)';
                } else {
                    detailsDiv.classList.add('hidden');
                    arrow.style.transform = 'rotate(0deg)';
                }
            }
        }

        // Helper functions for user-friendly messages - using our toast component
        function showSuccessMessage(message) {
            showToast(message, 'success');
        }

        function showErrorMessage(message) {
            showToast(message, 'error');
        }

        // Booking action variables
        let currentActionBookingId = null;

        // toggleDetails function moved to top of script section to avoid reference errors

        // Accept booking function
        function acceptBooking(bookingId) {
            if (confirm('Are you sure you want to accept this booking?')) {
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                };

                console.log('Request headers:', headers);
                console.log('Booking ID:', bookingId);

                fetch(`/provider/booking/${bookingId}/accept`, {
                    method: 'POST',
                    headers: headers
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);

                    // Handle redirects (301, 302, etc.)
                    if (response.redirected) {
                        console.log('Request was redirected to:', response.url);
                        showErrorMessage('You were redirected. Please check if you are logged in and try again.');
                        return { success: false, handled: true };
                    }

                    // Handle subscription requirement error (403)
                    if (response.status === 403) {
                        return response.text().then(text => {
                            console.log('403 response text:', text);
                            try {
                                const data = JSON.parse(text);
                                if (data.message && data.message.includes('subscription')) {
                                    showErrorMessage('Active subscription required to accept bookings. Please update your subscription to continue.');
                                    setTimeout(() => {
                                        window.location.href = '/profile?tab=time-spending';
                                    }, 3000);
                                    return { success: false, handled: true };
                                }
                                return data;
                            } catch (e) {
                                // Not JSON, probably HTML error page
                                showErrorMessage('Access denied. Please check your subscription status and try again.');
                                return { success: false, handled: true };
                            }
                        });
                    }

                    // Handle other HTTP errors
                    if (!response.ok) {
                        return response.text().then(text => {
                            console.log(`${response.status} response text:`, text);
                            throw new Error(`HTTP error! status: ${response.status}`);
                        });
                    }

                    // Try to parse as JSON
                    return response.text().then(text => {
                        console.log('Success response text:', text);
                        try {
                            return JSON.parse(text);
                        } catch (e) {
                            console.error('Failed to parse JSON:', text);
                            throw new Error('Invalid JSON response from server');
                        }
                    });
                })
                .then(data => {
                    if (data.handled) {
                        // Already handled subscription error above
                        return;
                    }

                    if (data.success) {
                        showSuccessMessage('Booking accepted successfully!');
                        location.reload();
                    } else {
                        showErrorMessage('Error: ' + (data.message || 'Failed to accept booking'));
                    }
                })
                .catch(error => {
                    console.error('Accept booking error:', error);
                    showErrorMessage('An error occurred while accepting the booking. Please try again.');
                });
            }
        }

        // Show reject modal
        function showRejectModal(bookingId) {
            currentActionBookingId = bookingId;
            document.getElementById('rejectModal').classList.remove('hidden');
        }

        // Close reject modal
        function closeRejectModal() {
            document.getElementById('rejectModal').classList.add('hidden');
            document.getElementById('rejectionReason').value = '';
            currentActionBookingId = null;
        }

        // Handle reject form submission
        document.getElementById('rejectForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const reason = document.getElementById('rejectionReason').value;
            if (!reason.trim()) {
                showErrorMessage('Please provide a reason for rejection.');
                return;
            }

            fetch(`/provider/booking/${currentActionBookingId}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ reason: reason })
            })
            .then(response => {
                // Handle subscription requirement error (403)
                if (response.status === 403) {
                    return response.json().then(data => {
                        if (data.message && data.message.includes('subscription')) {
                            showErrorMessage('Active subscription required to reject bookings. Please update your subscription to continue.');
                            setTimeout(() => {
                                window.location.href = '/profile?tab=time-spending';
                            }, 3000);
                            return { success: false, handled: true };
                        }
                        return data;
                    });
                }

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                if (data.handled) {
                    return;
                }

                if (data.success) {
                    closeRejectModal();
                    showSuccessMessage('Booking rejected successfully!');
                    location.reload();
                } else {
                    showErrorMessage('Error: ' + (data.message || 'Failed to reject booking'));
                }
            })
            .catch(error => {
                console.error('Reject booking error:', error);
                showErrorMessage('An error occurred while rejecting the booking. Please try again.');
            });
        });

        // Block client function
        function blockClient(bookingId) {
            if (confirm('Are you sure you want to block this client? They will not be able to book you again.')) {
                fetch(`/provider/booking/${bookingId}/block`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    // Handle subscription requirement error (403)
                    if (response.status === 403) {
                        return response.json().then(data => {
                            if (data.message && data.message.includes('subscription')) {
                                showErrorMessage('Active subscription required to block clients. Please update your subscription to continue.');
                                setTimeout(() => {
                                    window.location.href = '/profile?tab=time-spending';
                                }, 3000);
                                return { success: false, handled: true };
                            }
                            return data;
                        });
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    if (data.handled) {
                        return;
                    }

                    if (data.success) {
                        showSuccessMessage('Client blocked successfully.');
                        location.reload();
                    } else {
                        showErrorMessage('Error: ' + (data.message || 'Failed to block client'));
                    }
                })
                .catch(error => {
                    console.error('Block client error:', error);
                    showErrorMessage('An error occurred while blocking the client. Please try again.');
                });
            }
        }

        // Unblock user function
        function unblockUser(userId) {
            if (confirm('Are you sure you want to unblock this user? They will be able to book you again.')) {
                fetch(`/provider/user/${userId}/unblock`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    // Handle subscription requirement error (403)
                    if (response.status === 403) {
                        return response.json().then(data => {
                            if (data.message && data.message.includes('subscription')) {
                                showErrorMessage('Active subscription required to unblock users. Please update your subscription to continue.');
                                setTimeout(() => {
                                    window.location.href = '/profile?tab=time-spending';
                                }, 3000);
                                return { success: false, handled: true };
                            }
                            return data;
                        });
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    if (data.handled) {
                        return;
                    }

                    if (data.success) {
                        showSuccessMessage('User unblocked successfully.');
                        location.reload();
                    } else {
                        showErrorMessage('Error: ' + (data.message || 'Failed to unblock user'));
                    }
                })
                .catch(error => {
                    console.error('Unblock user error:', error);
                    showErrorMessage('An error occurred while unblocking the user. Please try again.');
                });
            }
        }

        // Open chat function
        function openChat(bookingId) {
            // Get booking details and redirect to chat
            fetch(`/booking/${bookingId}/chat-details`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success && data.chat_user_id) {
                        // Redirect to chat page with the other user
                        window.location.href = `/chat/${data.chat_user_id}`;
                    } else {
                        showErrorMessage(data.message || 'Unable to open chat. Please try again.');
                    }
                })
                .catch(error => {
                    showErrorMessage('An error occurred. Please try again.');
                });
        }

        // Close modal when clicking outside
        document.getElementById('rejectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRejectModal();
            }
        });

        // Clear notification badges when page loads (since all notifications are marked as read)
        document.addEventListener('DOMContentLoaded', function() {
            try {
                const desktopBadge = document.getElementById('notification-badge');
                const mobileBadge = document.getElementById('mobile-notification-badge');

                if (desktopBadge) {
                    desktopBadge.style.display = 'none';
                    desktopBadge.classList.remove('has-notifications');
                }
                if (mobileBadge) {
                    mobileBadge.style.display = 'none';
                    mobileBadge.classList.remove('has-notifications');
                }
            } catch (error) {
                // Error clearing notification badges - handle silently
            }
        });

        // Toggle notification details function is defined above - removing duplicate

        // Cancel booking functionality
        let currentCancelBookingId = null;

        function showCancelModal(bookingId) {
            currentCancelBookingId = bookingId;

            // Fetch booking details
            fetch(`/booking/${bookingId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const booking = data.booking;
                        const bookingDate = new Date(booking.booking_date);
                        const endTime = new Date(bookingDate.getTime() + (booking.duration_hours * 60 * 60 * 1000));

                        document.getElementById('cancelBookingDetails').innerHTML = `
                            <div class="text-sm">
                                <div class="flex justify-between mb-2">
                                    <span class="font-medium text-gray-700">Provider:</span>
                                    <span class="text-gray-900">${booking.provider.name}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="font-medium text-gray-700">Date:</span>
                                    <span class="text-gray-900">${formatDate(booking.booking_date)}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="font-medium text-gray-700">Time:</span>
                                    <span class="text-gray-900">${formatTime12Hour(bookingDate)} - ${formatTime12Hour(endTime)}</span>
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="font-medium text-gray-700">Duration:</span>
                                    <span class="text-gray-900">${booking.duration_hours} hour${booking.duration_hours > 1 ? 's' : ''}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Refund Amount:</span>
                                    <span class="text-green-600 font-semibold">₹${booking.total_amount}</span>
                                </div>
                            </div>
                        `;

                        document.getElementById('cancelModal').classList.remove('hidden');
                    } else {
                        showErrorMessage('Error loading booking details. Please try again.');
                    }
                })
                .catch(error => {
                    showErrorMessage('Error loading booking details. Please try again.');
                });
        }

        function closeCancelModal() {
            document.getElementById('cancelModal').classList.add('hidden');
            currentCancelBookingId = null;
        }

        function confirmCancelBooking() {
            if (!currentCancelBookingId) return;

            const confirmBtn = document.getElementById('confirmCancelBtn');
            const btnText = document.getElementById('cancelBtnText');
            const btnLoading = document.getElementById('cancelBtnLoading');

            // Show loading state
            confirmBtn.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');

            fetch(`/booking/${currentCancelBookingId}/cancel-pending`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    reason: 'Cancelled by client from notifications page'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showSuccessMessage(data.message);

                    // Close modal
                    closeCancelModal();

                    // Reload page to update notifications
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showErrorMessage(data.message || 'Error cancelling booking. Please try again.');
                }
            })
            .catch(error => {
                showErrorMessage('Error cancelling booking. Please try again.');
            })
            .finally(() => {
                // Reset button state
                confirmBtn.disabled = false;
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
            });
        }

        // Redirect to Update Booking Function
        function redirectToUpdateBooking(bookingId) {
            // Fetch booking details first to get provider ID
            fetch(`/booking/${bookingId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const booking = data.booking;
                        const providerId = booking.provider_id;

                        // Create URL with only booking ID for security
                        const updateUrl = `/find-person/${providerId}?update_booking=${bookingId}`;

                        // Redirect to provider page
                        window.location.href = updateUrl;
                    } else {
                        showErrorMessage('Error loading booking details. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error fetching booking details:', error);
                    showErrorMessage('Error loading booking details. Please try again.');
                });
        }

        // Update Booking Modal Functions (keeping for backward compatibility)
        let currentUpdateBookingId = null;

        function showUpdateBookingModal(bookingId) {
            currentUpdateBookingId = bookingId;

            // Fetch booking details
            fetch(`/booking/${bookingId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const booking = data.booking;
                        const bookingDate = new Date(booking.booking_date);

                        // Set the provider's hourly rate for calculations
                        currentProviderHourlyRate = booking.provider ? booking.provider.hourly_rate : 500;

                        // Populate modal with current booking details
                        document.getElementById('updateBookingDate').value = booking.booking_date.split('T')[0];
                        document.getElementById('updateBookingTime').value = bookingDate.toTimeString().slice(0, 5);
                        document.getElementById('updateDurationHours').value = booking.duration_hours;
                        document.getElementById('updateLocation').value = booking.meeting_location;

                        // Show current booking info
                        document.getElementById('currentBookingInfo').innerHTML = `
                            <div class="text-sm text-gray-600 mb-4">
                                <h4 class="font-medium text-gray-800 mb-2">Current Booking Details:</h4>
                                <div class="bg-gray-50 p-3 rounded-lg">
                                    <div class="grid grid-cols-2 gap-2 text-xs">
                                        <div><span class="font-medium">Date:</span> ${formatDate(booking.booking_date)}</div>
                                        <div><span class="font-medium">Time:</span> ${formatTime12Hour(bookingDate)}</div>
                                        <div><span class="font-medium">Duration:</span> ${booking.duration_hours}h</div>
                                        <div><span class="font-medium">Amount:</span> ₹${booking.total_amount}</div>
                                    </div>
                                </div>
                            </div>
                        `;

                        // Calculate initial amount
                        calculateUpdateAmount();

                        document.getElementById('updateBookingModal').classList.remove('hidden');
                    } else {
                        showErrorMessage('Error loading booking details. Please try again.');
                    }
                })
                .catch(error => {
                    showErrorMessage('Error loading booking details. Please try again.');
                });
        }

        function closeUpdateBookingModal() {
            document.getElementById('updateBookingModal').classList.add('hidden');
            currentUpdateBookingId = null;
        }

        let currentProviderHourlyRate = 500; // Default rate, will be updated when booking details are fetched

        function calculateUpdateAmount() {
            const durationHours = parseFloat(document.getElementById('updateDurationHours').value) || 0;
            const newAmount = durationHours * currentProviderHourlyRate;

            document.getElementById('updateTotalAmount').textContent = newAmount.toFixed(0);
            document.getElementById('updateTotalHours').textContent = durationHours === 1 ? '1 hour' : `${durationHours} hours`;
        }

        function submitUpdateBooking() {
            if (!currentUpdateBookingId) return;

            const updateBtn = document.getElementById('updateBookingBtn');
            const btnText = document.getElementById('updateBtnText');
            const btnLoading = document.getElementById('updateBtnLoading');

            // Show loading state
            updateBtn.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');

            const bookingDate = document.getElementById('updateBookingDate').value;
            const bookingTime = document.getElementById('updateBookingTime').value;
            const durationHours = document.getElementById('updateDurationHours').value;
            const location = document.getElementById('updateLocation').value;

            // Combine date and time
            const bookingDateTime = `${bookingDate}T${bookingTime}:00`;

            fetch(`/booking/${currentUpdateBookingId}/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    booking_date: bookingDateTime,
                    duration_hours: durationHours,
                    location: location
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showSuccessMessage(data.message);

                    // Close modal
                    closeUpdateBookingModal();

                    // Refresh page to show updated booking
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    if (data.errors) {
                        const errorMessages = Object.values(data.errors).flat().join('\n');
                        showErrorMessage('Validation errors:\n' + errorMessages);
                    } else {
                        showErrorMessage(data.message || 'Error updating booking. Please try again.');
                    }
                }
            })
            .catch(error => {
                console.error('Error updating booking:', error);
                showErrorMessage('Error updating booking. Please try again.');
            })
            .finally(() => {
                // Reset button state
                updateBtn.disabled = false;
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
            });
        }

        // Add event listener for duration change
        document.addEventListener('DOMContentLoaded', function() {
            const durationInput = document.getElementById('updateDurationHours');
            if (durationInput) {
                durationInput.addEventListener('change', calculateUpdateAmount);
            }
        });

        // Helper functions for formatting
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function formatTime12Hour(date) {
            if (typeof date === 'string') {
                date = new Date(date);
            }
            return date.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        }

        // Block client function
        function blockClient(bookingId) {
            if (confirm('Are you sure you want to block this client? They will not be able to book you again.')) {
                fetch(`/provider/booking/${bookingId}/block`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    // Handle subscription requirement error (403)
                    if (response.status === 403) {
                        return response.json().then(data => {
                            if (data.message && data.message.includes('subscription')) {
                                showErrorMessage('Active subscription required to block clients. Please update your subscription to continue.');
                                setTimeout(() => {
                                    window.location.href = '/profile?tab=time-spending';
                                }, 3000);
                                return { success: false, handled: true };
                            }
                            return data;
                        });
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    if (data.handled) {
                        return;
                    }

                    if (data.success) {
                        showSuccessMessage(data.message);
                        location.reload();
                    } else {
                        showErrorMessage(data.message || 'Error blocking client. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Block client error:', error);
                    showErrorMessage('Error blocking client. Please try again.');
                });
            }
        }

        // Unblock user function
        function unblockUser(userId) {
            if (confirm('Are you sure you want to unblock this user?')) {
                fetch(`/provider/user/${userId}/unblock`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    // Handle subscription requirement error (403)
                    if (response.status === 403) {
                        return response.json().then(data => {
                            if (data.message && data.message.includes('subscription')) {
                                showErrorMessage('Active subscription required to unblock users. Please update your subscription to continue.');
                                setTimeout(() => {
                                    window.location.href = '/profile?tab=time-spending';
                                }, 3000);
                                return { success: false, handled: true };
                            }
                            return data;
                        });
                    }

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    if (data.handled) {
                        return;
                    }

                    if (data.success) {
                        showSuccessMessage(data.message);
                        location.reload();
                    } else {
                        showErrorMessage(data.message || 'Error unblocking user. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Unblock user error:', error);
                    showErrorMessage('Error unblocking user. Please try again.');
                });
            }
        }

        // Open chat function
        function openChat(bookingId) {
            window.location.href = `/chat?booking_id=${bookingId}`;
        }

        // Show cancel modal
        function showCancelModal(bookingId) {
            currentActionBookingId = bookingId;

            // Fetch booking details to show in modal
            fetch(`/booking/${bookingId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const booking = data.booking;
                        const bookingDate = new Date(booking.booking_date);
                        const endTime = new Date(bookingDate.getTime() + (booking.duration_hours * 60 * 60 * 1000));

                        document.getElementById('cancelBookingDetails').innerHTML = `
                            <div class="text-sm space-y-1">
                                <div><strong>Provider:</strong> ${booking.provider.name}</div>
                                <div><strong>Date:</strong> ${bookingDate.toLocaleDateString()}</div>
                                <div><strong>Time:</strong> ${bookingDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} - ${endTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                                <div><strong>Duration:</strong> ${booking.duration_hours} hour${booking.duration_hours > 1 ? 's' : ''}</div>
                                <div><strong>Amount:</strong> ₹${booking.total_amount}</div>
                            </div>
                        `;

                        document.getElementById('cancelModal').classList.remove('hidden');
                    } else {
                        showErrorMessage('Error loading booking details. Please try again.');
                    }
                })
                .catch(error => {
                    showErrorMessage('Error loading booking details. Please try again.');
                });
        }

        // Close cancel modal
        function closeCancelModal() {
            document.getElementById('cancelModal').classList.add('hidden');
            currentActionBookingId = null;
        }

        // Confirm cancel booking
        function confirmCancelBooking() {
            if (!currentActionBookingId) return;

            const confirmBtn = document.getElementById('confirmCancelBtn');
            const btnText = document.getElementById('cancelBtnText');
            const btnLoading = document.getElementById('cancelBtnLoading');

            // Show loading state
            confirmBtn.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');

            fetch(`/booking/${currentActionBookingId}/cancel`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessMessage(data.message);
                    closeCancelModal();
                    location.reload();
                } else {
                    showErrorMessage(data.message || 'Error cancelling booking. Please try again.');
                }
            })
            .catch(error => {
                showErrorMessage('Error cancelling booking. Please try again.');
            })
            .finally(() => {
                // Reset button state
                confirmBtn.disabled = false;
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
            });
        }

        // Redirect to update booking
        function redirectToUpdateBooking(bookingId) {
            window.location.href = `/find-person/3?update_booking=${bookingId}`;
        }

        // Meeting Functions
        function startMeeting(bookingId) {
            openCameraModal('start', bookingId);
        }

        function endMeeting(bookingId) {
            openCameraModal('end', bookingId);
        }

        // Meeting Verification Camera Functions
        let currentStream = null;
        let currentPhotoType = null; // 'start' or 'end'
        let currentCameraBookingId = null;
        let capturedPhotoBlob = null;
        let currentLocation = null;

        async function openCameraModal(photoType, bookingId) {
            currentPhotoType = photoType;
            currentCameraBookingId = bookingId;
            currentLocation = null;

            const modal = document.getElementById('cameraModal');
            const title = document.getElementById('cameraModalTitle');

            title.textContent = photoType === 'start' ? 'Take Meeting Start Photo' : 'Take Meeting End Photo';

            // Reset modal state
            resetCameraModal();

            // Show modal first
            modal.classList.remove('hidden');

            // Check and request mandatory permissions
            const permissionsGranted = await checkAndRequestMandatoryPermissions();

            if (!permissionsGranted) {
                // Close modal if permissions not granted
                closeCameraModal();
                return;
            }

            // If permissions granted, automatically start camera
            await startCamera();
        }

        function closeCameraModal() {
            // Stop camera stream
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
            }

            // Reset variables
            currentPhotoType = null;
            currentCameraBookingId = null;
            capturedPhotoBlob = null;
            currentLocation = null;

            // Hide modal
            document.getElementById('cameraModal').classList.add('hidden');

            // Reset modal state
            resetCameraModal();
        }

        function resetCameraModal() {
            // Hide error
            document.getElementById('cameraError').classList.add('hidden');

            // Show camera container, hide photo preview
            document.getElementById('cameraContainer').classList.remove('hidden');
            document.getElementById('photoPreview').classList.add('hidden');

            // Reset buttons - hide start camera button since camera starts automatically
            document.getElementById('startCameraBtn').classList.add('hidden');
            document.getElementById('captureBtn').classList.add('hidden');
            document.getElementById('retakeBtn').classList.add('hidden');
            document.getElementById('uploadBtn').classList.add('hidden');

            // Reset upload button state
            const uploadBtn = document.getElementById('uploadBtn');
            const uploadBtnText = document.getElementById('uploadBtnText');
            const uploadBtnLoading = document.getElementById('uploadBtnLoading');
            uploadBtn.disabled = false;
            uploadBtnText.classList.remove('hidden');
            uploadBtnLoading.classList.add('hidden');
        }

        function showCameraError(message) {
            const errorDiv = document.getElementById('cameraError');
            errorDiv.querySelector('p').textContent = message;
            errorDiv.classList.remove('hidden');
        }

        // Check and request mandatory permissions (camera and location)
        async function checkAndRequestMandatoryPermissions() {
            const locationText = document.getElementById('locationText');
            const locationStatus = document.getElementById('locationStatus');

            try {
                // Update status to show checking permissions
                locationText.textContent = 'Checking camera and location permissions...';
                locationStatus.className = 'mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg';

                // Check camera permission first
                const cameraPermissionGranted = await checkCameraPermission();
                if (!cameraPermissionGranted) {
                    locationText.textContent = 'Camera access is required for meeting verification. Please allow camera access and try again.';
                    locationStatus.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
                    showCameraError('Camera access is required for meeting verification. Please allow camera access in your browser settings and refresh the page.');
                    return false;
                }

                // Check location permission
                const locationPermissionGranted = await checkLocationPermission();
                if (!locationPermissionGranted) {
                    locationText.textContent = 'Location access is required for meeting verification. Please allow location access and try again.';
                    locationStatus.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
                    showCameraError('Location access is required for meeting verification. Please allow location access in your browser settings and refresh the page.');
                    return false;
                }

                // Both permissions granted
                locationText.textContent = 'Permissions granted. Getting precise location...';
                locationStatus.className = 'mb-4 p-3 bg-green-50 border border-green-200 rounded-lg';

                // Get current location
                await getCurrentLocationForMeeting();
                return true;

            } catch (error) {
                console.error('Permission check error:', error);
                locationText.textContent = 'Error checking permissions. Please refresh and try again.';
                locationStatus.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
                showCameraError('Error checking permissions. Please refresh the page and try again.');
                return false;
            }
        }

        // Check camera permission
        async function checkCameraPermission() {
            try {
                // Try to get camera access
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });

                // Stop the stream immediately as we're just checking permission
                stream.getTracks().forEach(track => track.stop());
                return true;
            } catch (error) {
                console.error('Camera permission error:', error);
                return false;
            }
        }

        // Check location permission
        async function checkLocationPermission() {
            return new Promise((resolve) => {
                if (!navigator.geolocation) {
                    resolve(false);
                    return;
                }

                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        // Store the location immediately
                        currentLocation = {
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude,
                            accuracy: position.coords.accuracy
                        };
                        resolve(true);
                    },
                    function(error) {
                        console.error('Location permission error:', error);
                        resolve(false);
                    },
                    {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 60000
                    }
                );
            });
        }

        // Get current location for meeting verification (updated to be async)
        async function getCurrentLocationForMeeting() {
            const locationText = document.getElementById('locationText');
            const locationStatus = document.getElementById('locationStatus');

            return new Promise((resolve) => {
                if (currentLocation) {
                    // Location already obtained during permission check
                    getAddressFromCoordinates(currentLocation.latitude, currentLocation.longitude);
                    resolve(true);
                    return;
                }

                locationText.textContent = 'Getting precise location...';
                locationStatus.className = 'mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg';

                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        function(position) {
                            currentLocation = {
                                latitude: position.coords.latitude,
                                longitude: position.coords.longitude,
                                accuracy: position.coords.accuracy
                            };

                            // Get address from coordinates
                            getAddressFromCoordinates(position.coords.latitude, position.coords.longitude);
                            resolve(true);
                        },
                        function(error) {
                            locationText.textContent = 'Location access is required for meeting verification.';
                            locationStatus.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
                            resolve(false);
                        },
                        {
                            enableHighAccuracy: true,
                            timeout: 10000,
                            maximumAge: 60000
                        }
                    );
                } else {
                    locationText.textContent = 'Geolocation is not supported by this browser.';
                    locationStatus.className = 'mb-4 p-3 bg-red-50 border border-red-200 rounded-lg';
                    resolve(false);
                }
            });
        }

        // Get address from coordinates using reverse geocoding
        async function getAddressFromCoordinates(lat, lng) {
            const locationText = document.getElementById('locationText');
            const locationStatus = document.getElementById('locationStatus');

            try {
                // Try BigDataCloud API first
                const response = await fetch(`https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`);

                if (!response.ok) {
                    throw new Error('BigDataCloud API failed');
                }

                const data = await response.json();
                const address = data.locality || data.city || data.principalSubdivision || 'Unknown location';
                locationText.textContent = `Location: ${address}`;
                locationStatus.className = 'mb-4 p-3 bg-green-50 border border-green-200 rounded-lg';

                // Store full address in location object
                if (currentLocation) {
                    currentLocation.address = address;
                    currentLocation.fullAddress = data.locality ?
                        `${data.locality}, ${data.principalSubdivision}, ${data.countryName}` :
                        address;
                }
            } catch (error) {
                // Handle error silently

                try {
                    // Fallback to OpenStreetMap Nominatim
                    const fallbackResponse = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=10&addressdetails=1`);

                    if (!fallbackResponse.ok) {
                        throw new Error('Nominatim API failed');
                    }

                    const fallbackData = await fallbackResponse.json();
                    const address = fallbackData.address?.city || fallbackData.address?.town || fallbackData.address?.village || fallbackData.address?.state || 'Unknown location';
                    locationText.textContent = `Location: ${address}`;
                    locationStatus.className = 'mb-4 p-3 bg-green-50 border border-green-200 rounded-lg';

                    // Store address data
                    if (currentLocation) {
                        currentLocation.address = address;
                        currentLocation.fullAddress = fallbackData.display_name || `${address}`;
                    }
                } catch (fallbackError) {
                    console.error('Error getting address from Nominatim:', fallbackError);
                    // Use coordinates as final fallback
                    locationText.textContent = `Location: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
                    locationStatus.className = 'mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg';

                    // Store coordinates as fallback
                    if (currentLocation) {
                        currentLocation.address = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
                        currentLocation.fullAddress = `Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
                    }
                }
            }
        }

        async function startCamera() {
            try {
                // Check if getUserMedia is supported
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('Camera access is not supported in this browser.');
                }

                // Request camera access
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'user',
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });

                currentStream = stream;
                const video = document.getElementById('cameraPreview');
                video.srcObject = stream;

                // Update button visibility
                document.getElementById('startCameraBtn').classList.add('hidden');
                document.getElementById('captureBtn').classList.remove('hidden');

                // Hide error if it was showing
                document.getElementById('cameraError').classList.add('hidden');

            } catch (error) {
                let errorMessage = 'Unable to access camera. ';

                if (error.name === 'NotAllowedError') {
                    errorMessage += 'Please allow camera permissions in your browser settings and refresh the page.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'No camera found on this device.';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += 'Camera access is not supported in this browser.';
                } else if (error.name === 'NotReadableError') {
                    errorMessage += 'Camera is already in use by another application.';
                } else {
                    errorMessage += 'Please ensure you have granted camera permissions and try again.';
                }

                showCameraError(errorMessage);
            }
        }

        function capturePhoto() {
            const video = document.getElementById('cameraPreview');
            const canvas = document.getElementById('photoCanvas');
            const context = canvas.getContext('2d');

            // Set canvas dimensions to match video
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            // Draw video frame to canvas
            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            // Convert canvas to blob
            canvas.toBlob(function(blob) {
                capturedPhotoBlob = blob;

                // Show captured photo
                const capturedImg = document.getElementById('capturedPhoto');
                capturedImg.src = URL.createObjectURL(blob);

                // Hide camera, show photo preview
                document.getElementById('cameraContainer').classList.add('hidden');
                document.getElementById('photoPreview').classList.remove('hidden');

                // Update button visibility
                document.getElementById('captureBtn').classList.add('hidden');
                document.getElementById('retakeBtn').classList.remove('hidden');
                document.getElementById('uploadBtn').classList.remove('hidden');

                // Stop camera stream
                if (currentStream) {
                    currentStream.getTracks().forEach(track => track.stop());
                    currentStream = null;
                }
            }, 'image/jpeg', 0.8);
        }

        async function retakePhoto() {
            // Reset to camera view
            document.getElementById('cameraContainer').classList.remove('hidden');
            document.getElementById('photoPreview').classList.add('hidden');

            // Reset buttons - hide all initially
            document.getElementById('startCameraBtn').classList.add('hidden');
            document.getElementById('captureBtn').classList.add('hidden');
            document.getElementById('retakeBtn').classList.add('hidden');
            document.getElementById('uploadBtn').classList.add('hidden');

            // Clear captured photo
            capturedPhotoBlob = null;

            // Restart camera automatically
            await startCamera();
        }

        async function uploadPhoto() {
            if (!capturedPhotoBlob || !currentPhotoType || !currentCameraBookingId) {
                showCameraError('Missing photo or booking information. Please try again.');
                return;
            }

            // Check if location is available (now mandatory)
            if (!currentLocation || !currentLocation.latitude || !currentLocation.longitude) {
                showCameraError('Location is required for meeting verification. Please ensure location access is enabled and try again.');
                return;
            }

            const uploadBtn = document.getElementById('uploadBtn');
            const uploadBtnText = document.getElementById('uploadBtnText');
            const uploadBtnLoading = document.getElementById('uploadBtnLoading');

            // Show loading state
            uploadBtn.disabled = true;
            uploadBtnText.classList.add('hidden');
            uploadBtnLoading.classList.remove('hidden');

            try {
                const formData = new FormData();
                formData.append('photo', capturedPhotoBlob, `meeting_${currentPhotoType}_photo.jpg`);

                // Add location data (now mandatory)
                formData.append('latitude', currentLocation.latitude);
                formData.append('longitude', currentLocation.longitude);
                formData.append('accuracy', currentLocation.accuracy || 0);
                formData.append('address', currentLocation.address || '');
                formData.append('full_address', currentLocation.fullAddress || '');

                const endpoint = currentPhotoType === 'start'
                    ? `/meeting-verification/${currentCameraBookingId}/start-photo`
                    : `/meeting-verification/${currentCameraBookingId}/end-photo`;

                const response = await fetch(endpoint, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Show success message
                    showSuccessMessage(data.message);

                    // Close modal
                    closeCameraModal();

                    // Refresh page to show updated verification status
                    location.reload();
                } else {
                    showCameraError(data.message || 'Failed to upload photo. Please try again.');
                }

            } catch (error) {
                showCameraError('Network error. Please check your connection and try again.');
            } finally {
                // Reset button state
                uploadBtn.disabled = false;
                uploadBtnText.classList.remove('hidden');
                uploadBtnLoading.classList.add('hidden');
            }
        }



        // Review-related functions
        let currentReviewBookingId = null;
        let selectedRating = 0;

        function openReviewModal(bookingId, otherUserName) {
            currentReviewBookingId = bookingId;

            // Load booking details
            loadBookingDetailsForReview(bookingId, otherUserName);

            // Show modal
            document.getElementById('reviewModal').classList.remove('hidden');

            // Reset form
            resetReviewForm();
        }

        function closeReviewModal() {
            document.getElementById('reviewModal').classList.add('hidden');
            currentReviewBookingId = null;
            selectedRating = 0;
            resetReviewForm();
        }

        function resetReviewForm() {
            // Reset rating stars
            document.querySelectorAll('.star-btn').forEach(star => {
                star.classList.remove('text-yellow-400');
                star.classList.add('text-gray-300');
            });

            // Reset form fields
            document.getElementById('reviewText').value = '';
            document.getElementById('isAnonymous').checked = false;
            document.getElementById('selectedRating').value = '';
            document.getElementById('ratingText').textContent = 'Click to rate';
            selectedRating = 0;
        }

        async function loadBookingDetailsForReview(bookingId, otherUserName) {
            try {
                // Set basic info
                document.getElementById('reviewUserName').textContent = otherUserName || 'Loading...';
                document.getElementById('reviewBookingId').value = bookingId;

                // You can add more detailed booking info loading here if needed
                document.getElementById('reviewBookingDetails').textContent = 'Rate your experience with this meeting';

            } catch (error) {
                console.error('Error loading booking details:', error);
                document.getElementById('reviewUserName').textContent = 'Unknown User';
                document.getElementById('reviewBookingDetails').textContent = 'Rate your experience';
            }
        }

        // Star rating functionality
        document.addEventListener('DOMContentLoaded', function() {
            const starButtons = document.querySelectorAll('.star-btn');

            starButtons.forEach(star => {
                star.addEventListener('click', function() {
                    const rating = parseInt(this.dataset.rating);
                    selectedRating = rating;
                    document.getElementById('selectedRating').value = rating;

                    // Update star display
                    starButtons.forEach((s, index) => {
                        if (index < rating) {
                            s.classList.remove('text-gray-300');
                            s.classList.add('text-yellow-400');
                        } else {
                            s.classList.remove('text-yellow-400');
                            s.classList.add('text-gray-300');
                        }
                    });

                    // Update rating text
                    const ratingTexts = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
                    document.getElementById('ratingText').textContent = ratingTexts[rating] || 'Click to rate';
                });

                // Hover effect
                star.addEventListener('mouseenter', function() {
                    const rating = parseInt(this.dataset.rating);
                    starButtons.forEach((s, index) => {
                        if (index < rating) {
                            s.classList.add('text-yellow-300');
                        }
                    });
                });

                star.addEventListener('mouseleave', function() {
                    starButtons.forEach(s => {
                        s.classList.remove('text-yellow-300');
                    });
                });
            });
        });

        // Helper functions for showing messages
        function showSuccessMessage(message) {
            // Create a temporary success message
            const successDiv = document.createElement('div');
            successDiv.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
            successDiv.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span>${message}</span>
                </div>
            `;
            document.body.appendChild(successDiv);

            // Remove after 3 seconds
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.parentNode.removeChild(successDiv);
                }
            }, 3000);
        }

        function showErrorMessage(message) {
            // Create a temporary error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
            errorDiv.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <span>${message}</span>
                </div>
            `;
            document.body.appendChild(errorDiv);

            // Remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }

        async function submitReview(event) {
            event.preventDefault();

            if (!selectedRating || selectedRating < 1) {
                showErrorMessage('Please select a rating before submitting.');
                return;
            }

            if (!currentReviewBookingId) {
                showErrorMessage('Invalid booking ID. Please try again.');
                return;
            }

            const submitBtn = document.getElementById('submitReviewBtn');
            const submitText = document.getElementById('submitReviewText');
            const submitLoading = document.getElementById('submitReviewLoading');

            // Disable button and show loading
            submitBtn.disabled = true;
            submitText.classList.add('hidden');
            submitLoading.classList.remove('hidden');

            try {
                // Get CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (!csrfToken) {
                    throw new Error('CSRF token not found');
                }

                // Prepare data as JSON
                const reviewData = {
                    booking_id: currentReviewBookingId,
                    rating: selectedRating,
                    review_text: document.getElementById('reviewText').value || '',
                    is_anonymous: document.getElementById('isAnonymous').checked
                };

                console.log('Submitting review:', reviewData);

                const response = await fetch('/rating/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(reviewData)
                });

                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Response error text:', errorText);
                    throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
                }

                const data = await response.json();
                console.log('Response data:', data);

                if (data.success) {
                    showSuccessMessage('Review submitted successfully! Thank you for your feedback.');
                    closeReviewModal();

                    // Refresh page to update notifications
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showErrorMessage(data.message || 'Failed to submit review. Please try again.');
                    if (data.errors) {
                        console.error('Validation errors:', data.errors);
                    }
                }

            } catch (error) {
                console.error('Error submitting review:', error);
                showErrorMessage('Network error: ' + error.message + '. Please check your connection and try again.');
            } finally {
                // Reset button state
                submitBtn.disabled = false;
                submitText.classList.remove('hidden');
                submitLoading.classList.add('hidden');
            }
        }

        function viewProfile(userId) {
            // Redirect to user's profile page to view reviews
            window.location.href = `/profile/${userId}`;
        }

        // My Reviews Modal functionality
        let currentMyReviewsPage = 1;
        let hasMoreMyReviews = false;
        let isLoadingMyReviews = false;

        function openMyReviewsModal() {
            document.getElementById('myReviewsModal').classList.remove('hidden');
            currentMyReviewsPage = 1;
            hasMoreMyReviews = false;
            loadMyReviews();
        }

        function closeMyReviewsModal() {
            document.getElementById('myReviewsModal').classList.add('hidden');
            // Reset modal state
            document.getElementById('reviewsLoading').classList.remove('hidden');
            document.getElementById('myReviewsContainer').classList.add('hidden');
            document.getElementById('noReviewsMessage').classList.add('hidden');
            document.getElementById('reviewsError').classList.add('hidden');
            document.getElementById('reviewsStats').classList.add('hidden');
            document.getElementById('reviewsPagination').classList.add('hidden');
        }

        async function loadMyReviews() {
            if (isLoadingMyReviews) return;

            isLoadingMyReviews = true;

            // Show loading state
            document.getElementById('reviewsLoading').classList.remove('hidden');
            document.getElementById('myReviewsContainer').classList.add('hidden');
            document.getElementById('noReviewsMessage').classList.add('hidden');
            document.getElementById('reviewsError').classList.add('hidden');
            document.getElementById('reviewsStats').classList.add('hidden');
            document.getElementById('reviewsPagination').classList.add('hidden');

            try {
                const response = await fetch(`/reviews/user/{{ Auth::user()->id }}?page=${currentMyReviewsPage}`);
                const data = await response.json();


                    console.log('=== END DEBUG ===');
                }

                if (data.success) {
                    if (data.reviews.data.length > 0) {
                        displayMyReviews(data.reviews.data, currentMyReviewsPage === 1);
                        displayReviewsStats(data.statistics);

                        // Check if there are more reviews
                        hasMoreMyReviews = data.reviews.current_page < data.reviews.last_page;

                        if (hasMoreMyReviews) {
                            document.getElementById('reviewsPagination').classList.remove('hidden');
                        }

                        document.getElementById('myReviewsContainer').classList.remove('hidden');
                    } else if (currentMyReviewsPage === 1) {
                        // No reviews at all
                        document.getElementById('noReviewsMessage').classList.remove('hidden');
                        // Still show stats if available (edge case)
                        if (data.statistics && data.statistics.total_reviews > 0) {
                            displayReviewsStats(data.statistics);
                        }
                    }

                    document.getElementById('reviewsLoading').classList.add('hidden');
                } else {
                    throw new Error(data.message || 'Failed to load reviews');
                }
            } catch (error) {
                console.error('Error loading reviews:', error);
                document.getElementById('reviewsLoading').classList.add('hidden');
                document.getElementById('reviewsError').classList.remove('hidden');
            } finally {
                isLoadingMyReviews = false;
            }
        }

        function displayMyReviews(reviews, clearContainer = false) {
            const container = document.getElementById('myReviewsContainer');

            if (clearContainer) {
                container.innerHTML = '';
            }

            const reviewsHtml = reviews.map(review => {
                const reviewDate = new Date(review.created_at);
                const formattedDate = reviewDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });

                const bookingDate = review.booking ? new Date(review.booking.booking_date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                }) : 'N/A';

                // Handle profile picture URL with proper fallbacks
                let profilePictureUrl = '{{ asset("images/default-avatar.png") }}';

                // Debug log for this specific review
                if (currentMyReviewsPage === 1 && reviews.indexOf(review) < 3) {
                    console.log(`Review ${reviews.indexOf(review)} - Reviewer:`, review.reviewer);
                }

                // For anonymous reviews, always use default avatar
                if (review.is_anonymous) {
                    profilePictureUrl = '{{ asset("images/default-avatar.png") }}';
                    console.log('Using default avatar for anonymous review');
                } else {
                    // Check multiple ways to get profile picture for non-anonymous reviews
                    if (review.reviewer) {
                        if (review.reviewer.profile_picture_url) {
                            profilePictureUrl = review.reviewer.profile_picture_url;
                            console.log('Using profile_picture_url:', profilePictureUrl);
                        } else if (review.reviewer.profile_picture) {
                            // Manually construct URL from profile_picture field
                            profilePictureUrl = '{{ asset("storage") }}/' + review.reviewer.profile_picture;
                            console.log('Using manual construction:', profilePictureUrl);
                        } else if (review.reviewer.name) {
                            // Use UI Avatars as fallback with user's name
                            const userName = review.reviewer.name;
                            profilePictureUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&background=e5e7eb&color=6b7280&size=48`;
                            console.log('Using UI Avatars:', profilePictureUrl);
                        }
                    } else {
                        console.log('No reviewer data found for review');
                    }
                }

                const reviewerName = review.is_anonymous ? 'Anonymous User' : (review.reviewer ? review.reviewer.name : 'Unknown User');

                return `
                    <div class="review-card bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 rounded-full border-2 border-gray-200 flex-shrink-0 overflow-hidden bg-gray-100">
                                <img src="${profilePictureUrl}"
                                     alt="${reviewerName}"
                                     class="w-full h-full object-cover"
                                     onerror="handleImageError(this)"
                                     onload="console.log('Image loaded:', this.src)"
                                     style="background-color: #f3f4f6;">
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <div>
                                        <h5 class="font-semibold text-gray-900">
                                            ${reviewerName}
                                        </h5>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <div class="flex">
                                                ${generateStarRating(review.rating)}
                                            </div>
                                            <span class="text-sm text-gray-600">${review.rating}/5</span>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-xs text-gray-500">${formattedDate}</span>
                                        ${review.booking ? `<div class="text-xs text-gray-400 mt-1">Meeting: ${bookingDate}</div>` : ''}
                                    </div>
                                </div>
                                ${review.review_text ? `
                                    <div class="mt-2 p-3 bg-gray-50 rounded-lg">
                                        <p class="text-gray-700 text-sm">${review.review_text}</p>
                                    </div>
                                ` : ''}
                                ${review.booking ? `
                                    <div class="mt-2 text-xs text-gray-500">
                                        <span class="inline-flex items-center">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Duration: ${review.booking.duration_hours} hour${review.booking.duration_hours > 1 ? 's' : ''}
                                        </span>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML += reviewsHtml;
        }

        function displayReviewsStats(statistics) {
            if (!statistics) return;

            document.getElementById('averageRating').textContent = statistics.average_rating.toFixed(1);
            document.getElementById('totalReviews').textContent = statistics.total_reviews;

            // Generate stars for average rating
            const averageStarsContainer = document.getElementById('averageStars');
            averageStarsContainer.innerHTML = generateStarRating(Math.round(statistics.average_rating));

            document.getElementById('reviewsStats').classList.remove('hidden');
        }

        function generateStarRating(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
                } else {
                    stars += '<svg class="w-4 h-4 text-gray-300 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
                }
            }
            return stars;
        }

        async function loadMoreMyReviews() {
            if (isLoadingMyReviews || !hasMoreMyReviews) return;

            currentMyReviewsPage++;

            // Show loading state on button
            const loadMoreBtn = document.getElementById('loadMoreReviews');
            const loadMoreText = document.getElementById('loadMoreText');
            const loadMoreLoading = document.getElementById('loadMoreLoading');

            loadMoreText.classList.add('hidden');
            loadMoreLoading.classList.remove('hidden');
            loadMoreBtn.disabled = true;

            try {
                const response = await fetch(`/reviews/user/{{ Auth::user()->id }}?page=${currentMyReviewsPage}`);
                const data = await response.json();

                if (data.success && data.reviews.data.length > 0) {
                    displayMyReviews(data.reviews.data, false);

                    // Check if there are more reviews
                    hasMoreMyReviews = data.reviews.current_page < data.reviews.last_page;

                    if (!hasMoreMyReviews) {
                        document.getElementById('reviewsPagination').classList.add('hidden');
                    }
                } else {
                    hasMoreMyReviews = false;
                    document.getElementById('reviewsPagination').classList.add('hidden');
                }
            } catch (error) {
                console.error('Error loading more reviews:', error);
                showErrorMessage('Failed to load more reviews. Please try again.');
                currentMyReviewsPage--; // Revert page increment
            } finally {
                loadMoreText.classList.remove('hidden');
                loadMoreLoading.classList.add('hidden');
                loadMoreBtn.disabled = false;
            }
        }

        // Close modal when clicking outside
        document.getElementById('myReviewsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMyReviewsModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !document.getElementById('myReviewsModal').classList.contains('hidden')) {
                closeMyReviewsModal();
            }
        });

        // Handle image loading errors
        function handleImageError(img) {
            console.log('Image failed to load:', img.src);

            // Try different fallback options
            const fallbacks = [
                '{{ asset("images/default-avatar.png") }}',
                '{{ asset("images/default-user.png") }}',
                '{{ asset("assets/images/default-avatar.png") }}',
                'https://ui-avatars.com/api/?name=' + encodeURIComponent(img.alt || 'User') + '&background=e5e7eb&color=6b7280&size=48&rounded=true',
                'https://via.placeholder.com/48x48/e5e7eb/6b7280?text=' + encodeURIComponent((img.alt || 'U').charAt(0).toUpperCase()),
                'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMiIgeT0iMTIiPgo8cGF0aCBkPSJNMTYgN0E0IDQgMCAxIDEgOCA3QTQgNCAwIDAgMSAxNiA3Wk0xMiAxNEE3IDcgMCAwIDAgNSAyMUgxOUE3IDcgMCAwIDAgMTIgMTRaIiBmaWxsPSIjNkI3MjgwIi8+Cjwvc3ZnPgo8L3N2Zz4K'
            ];

            // Get current fallback index from data attribute, or start at 0
            let currentIndex = parseInt(img.dataset.fallbackIndex || '0');

            if (currentIndex < fallbacks.length - 1) {
                currentIndex++;
                img.dataset.fallbackIndex = currentIndex;
                img.src = fallbacks[currentIndex];

            } else {

                // Remove onerror to prevent infinite loop
                img.onerror = null;
            }
        }

        // Dispute functionality
        function showDisputeModal(bookingId) {
            document.getElementById('disputeBookingId').value = bookingId;
            document.getElementById('disputeModal').classList.remove('hidden');

            // Reset form
            document.getElementById('disputeForm').reset();
            document.getElementById('disputeBookingId').value = bookingId;
            document.getElementById('reasonCharCount').textContent = '0';
            document.getElementById('photoPreviewContainer').classList.add('hidden');
            document.getElementById('photoPreviewContainer').innerHTML = '';
        }

        function closeDisputeModal() {
            document.getElementById('disputeModal').classList.add('hidden');
        }

        function closeDisputeDetailsModal() {
            document.getElementById('disputeDetailsModal').classList.add('hidden');
        }

        // Character count for dispute reason
        document.addEventListener('DOMContentLoaded', function() {
            const reasonTextarea = document.getElementById('disputeReason');
            const charCount = document.getElementById('reasonCharCount');

            if (reasonTextarea && charCount) {
                reasonTextarea.addEventListener('input', function() {
                    charCount.textContent = this.value.length;
                });
            }

            // Photo preview functionality
            const evidencePhotos = document.getElementById('evidencePhotos');
            if (evidencePhotos) {
                evidencePhotos.addEventListener('change', function(e) {
                    const files = Array.from(e.target.files);
                    const container = document.getElementById('photoPreviewContainer');

                    if (files.length > 5) {
                        showErrorMessage('You can only upload up to 5 photos.');
                        e.target.value = '';
                        return;
                    }

                    container.innerHTML = '';

                    if (files.length > 0) {
                        container.classList.remove('hidden');

                        files.forEach((file, index) => {
                            if (file.size > 5 * 1024 * 1024) { // 5MB
                                showErrorMessage(`File ${file.name} is too large. Maximum size is 5MB.`);
                                return;
                            }

                            const reader = new FileReader();
                            reader.onload = function(e) {
                                const div = document.createElement('div');
                                div.className = 'relative';
                                div.innerHTML = `
                                    <img src="${e.target.result}" class="w-full h-20 object-cover rounded border">
                                    <div class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs cursor-pointer" onclick="removePhoto(${index})">×</div>
                                `;
                                container.appendChild(div);
                            };
                            reader.readAsDataURL(file);
                        });
                    } else {
                        container.classList.add('hidden');
                    }
                });
            }

            // Dispute form submission
            const disputeForm = document.getElementById('disputeForm');
            if (disputeForm) {
                disputeForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitDispute();
                });
            }
        });

        function removePhoto(index) {
            const input = document.getElementById('evidencePhotos');
            const dt = new DataTransfer();
            const files = Array.from(input.files);

            files.forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });

            input.files = dt.files;
            input.dispatchEvent(new Event('change'));
        }

        async function submitDispute() {
            const form = document.getElementById('disputeForm');
            const formData = new FormData(form);

            const submitBtn = document.getElementById('submitDisputeBtn');
            const btnText = document.getElementById('disputeBtnText');
            const btnLoading = document.getElementById('disputeBtnLoading');

            // Show loading state
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
            submitBtn.disabled = true;

            try {
                const response = await fetch('/dispute/raise', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage(data.message);
                    closeDisputeModal();
                    // Refresh the page to show updated dispute status
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    if (data.errors) {
                        const errorMessages = Object.values(data.errors).flat().join(', ');
                        showErrorMessage(errorMessages);
                    } else {
                        showErrorMessage(data.message || 'Failed to submit dispute. Please try again.');
                    }
                }
            } catch (error) {
                console.error('Error submitting dispute:', error);
                showErrorMessage('An error occurred while submitting the dispute. Please try again.');
            } finally {
                // Reset loading state
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
                submitBtn.disabled = false;
            }
        }

        async function viewDisputeDetails(bookingId) {
            try {
                const response = await fetch(`/dispute/details/${bookingId}`, {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    }
                });

                const data = await response.json();

                if (data.success) {
                    const dispute = data.dispute;
                    const content = document.getElementById('disputeDetailsContent');

                    let evidenceHtml = '';
                    if (dispute.evidence && dispute.evidence.length > 0) {
                        evidenceHtml = `
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2">Evidence Photos:</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    ${dispute.evidence.map(item => `
                                        <img src="/storage/${item.path}" class="w-full h-20 object-cover rounded border cursor-pointer"
                                             onclick="window.open('/storage/${item.path}', '_blank')">
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    }

                    content.innerHTML = `
                        <div class="space-y-4">
                            <div class="bg-orange-50 border border-orange-200 rounded-lg p-3">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-orange-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <div>
                                        <div class="font-medium text-orange-800">${dispute.dispute_type_display}</div>
                                        <div class="text-sm text-orange-700">${dispute.dispute_status_display}</div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">Reported Issue:</h4>
                                <p class="text-gray-700 bg-gray-50 p-3 rounded-lg">${dispute.dispute_reason}</p>
                            </div>

                            ${evidenceHtml}

                            <div class="text-sm text-gray-500">
                                <div>Reported on: ${new Date(dispute.disputed_at).toLocaleDateString()}</div>
                                ${dispute.resolved_at ? `<div>Resolved on: ${new Date(dispute.resolved_at).toLocaleDateString()}</div>` : ''}
                            </div>

                            ${dispute.admin_notes ? `
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">Admin Notes:</h4>
                                    <p class="text-gray-700 bg-blue-50 p-3 rounded-lg">${dispute.admin_notes}</p>
                                </div>
                            ` : ''}
                        </div>
                    `;

                    document.getElementById('disputeDetailsModal').classList.remove('hidden');
                } else {
                    showErrorMessage(data.message || 'Failed to load dispute details.');
                }
            } catch (error) {
                showErrorMessage('An error occurred while loading dispute details.');
            }
        }


    </script>

    <!-- Toast Notifications -->
    <x-toast />
</x-app-layout>
