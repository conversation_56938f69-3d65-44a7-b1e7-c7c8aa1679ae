@extends('layouts.admin')

@section('page-title', 'Sugar Partner Profile')

@section('page-description', 'Detailed profile view for ' . $user->name)

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <a href="{{ route('admin.sugar-partners.index') }}" class="text-decoration-none">Sugar Partners</a>
    </li>
    <li class="breadcrumb-item">
        <span class="text-muted">{{ $user->name }}</span>
    </li>
@endsection

@section('header-actions')
    <div class="d-flex gap-2">
        <a href="{{ route('admin.sugar-partners.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>Back to List
        </a>
        @if($user->is_suspended ?? false)
            <button type="button" class="btn btn-success btn-activate-user" data-user-id="{{ $user->id }}">
                <i class="bi bi-check-circle me-2"></i>Activate User
            </button>
        @else
            <button type="button" class="btn btn-warning btn-suspend-user" data-user-id="{{ $user->id }}">
                <i class="bi bi-pause-circle me-2"></i>Suspend User
            </button>
        @endif
    </div>
@endsection

@section('content')
<div class="row">
    <!-- Profile Overview -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body text-center">
                @if($user->profile_picture)
                    <img class="rounded-circle mb-3" src="{{ asset('storage/' . $user->profile_picture) }}" 
                         alt="{{ $user->name }}" style="width: 120px; height: 120px; object-fit: cover;">
                @else
                    <div class="rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center"
                         style="width: 120px; height: 120px; background: linear-gradient(135deg, #e11d48 0%, #f43f5e 100%);">
                        <span class="text-white fw-bold fs-1">{{ substr($user->name, 0, 1) }}</span>
                    </div>
                @endif
                
                <h4 class="fw-bold mb-1">{{ $user->name }}</h4>
                <p class="text-muted mb-2">ID: {{ $user->id }}</p>
                
                @if($user->is_suspended ?? false)
                    <span class="badge bg-danger-subtle text-danger mb-3">
                        <i class="bi bi-pause-circle me-1"></i>Suspended
                    </span>
                @else
                    <span class="badge bg-success-subtle text-success mb-3">
                        <i class="bi bi-check-circle me-1"></i>Active
                    </span>
                @endif

                <div class="d-flex justify-content-center gap-2 mb-3">
                    @if($user->email_verified_at)
                        <span class="badge bg-success-subtle text-success">
                            <i class="bi bi-check-circle me-1"></i>Email Verified
                        </span>
                    @else
                        <span class="badge bg-warning-subtle text-warning">
                            <i class="bi bi-exclamation-circle me-1"></i>Email Unverified
                        </span>
                    @endif
                </div>

                <div class="text-start">
                    <small class="text-muted">Member since</small>
                    <div class="fw-medium">{{ $user->created_at->format('M d, Y') }}</div>
                </div>
            </div>
        </div>

        <!-- Sugar Partner Types -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-heart me-2 text-danger"></i>Sugar Partner Preferences
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <!-- What I Am Section -->
                    <div class="col-md-6">
                        <h6 class="fw-semibold text-primary mb-2">
                            <i class="bi bi-person-check me-1"></i>What I Am
                        </h6>
                        @php $whatIAm = $user->getWhatIAmForAdmin(); @endphp
                        @if($whatIAm !== 'None')
                            <span class="badge bg-primary-subtle text-primary">
                                <i class="bi bi-person-check me-1"></i>{{ $whatIAm }}
                            </span>
                        @else
                            <span class="text-muted">Not specified</span>
                        @endif
                    </div>

                    <!-- What I Want Section -->
                    <div class="col-md-6">
                        <h6 class="fw-semibold text-danger mb-2">
                            <i class="bi bi-heart me-1"></i>What I Want
                        </h6>
                        @php $whatIWant = $user->getWhatIWantForAdmin(); @endphp
                        @if(!in_array('None', $whatIWant))
                            <div class="d-flex flex-wrap gap-1">
                                @foreach($whatIWant as $type)
                                    <span class="badge bg-danger-subtle text-danger">
                                        <i class="bi bi-heart me-1"></i>{{ $type }}
                                    </span>
                                @endforeach
                            </div>
                        @else
                            <span class="text-muted">Not specified</span>
                        @endif
                    </div>
                </div>

                @if($user->getWhatIAmForAdmin() === 'None' && in_array('None', $user->getWhatIWantForAdmin()))
                    <div class="alert alert-info mt-3 mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        This user has not specified any sugar partner preferences.
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Detailed Information -->
    <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-person me-2 text-primary"></i>Basic Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Email Address</label>
                        <div class="d-flex align-items-center">
                            <span>{{ $user->email }}</span>
                            <i class="bi {{ $user->email_verified_at ? 'bi-check-circle text-success' : 'bi-x-circle text-danger' }} ms-2"></i>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Phone Number</label>
                        <div>{{ $user->contact_number ?? 'Not provided' }}</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Gender</label>
                        <div>
                            @if($user->gender === 'male')
                                <span class="badge bg-info-subtle text-info">
                                    <i class="bi bi-person me-1"></i>Male
                                </span>
                            @elseif($user->gender === 'female')
                                <span class="badge bg-warning-subtle text-warning">
                                    <i class="bi bi-person-dress me-1"></i>Female
                                </span>
                            @else
                                <span class="text-muted">Not specified</span>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Date of Birth</label>
                        <div>{{ $user->date_of_birth ? $user->date_of_birth->format('M d, Y') : 'Not provided' }}</div>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-medium text-muted">Interests</label>
                        <div>
                            @if($user->interests)
                                @foreach(explode(',', $user->interests) as $interest)
                                    <span class="badge bg-secondary-subtle text-secondary me-1">{{ trim($interest) }}</span>
                                @endforeach
                            @else
                                <span class="text-muted">Not specified</span>
                            @endif
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-medium text-muted">Expectations</label>
                        <div>{{ $user->expectation ?? 'Not provided' }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sugar Partner Information -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-heart me-2 text-danger"></i>Sugar Partner Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label fw-medium text-muted">Sugar Partner Bio</label>
                        <div class="bg-light p-3 rounded">
                            {{ $user->sugar_partner_bio ?? 'No bio provided' }}
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-medium text-muted">Sugar Partner Expectations</label>
                        <div class="bg-light p-3 rounded">
                            {{ $user->sugar_partner_expectations ?? 'No expectations provided' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gallery Images -->
        @if($user->galleryImages->count() > 0)
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-images me-2 text-success"></i>Gallery Images ({{ $user->galleryImages->count() }})
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    @foreach($user->galleryImages as $image)
                        <div class="col-md-4">
                            <div class="position-relative">
                                <img src="{{ asset('storage/' . $image->image_path) }}" 
                                     alt="Gallery Image" 
                                     class="img-fluid rounded shadow-sm"
                                     style="width: 100%; height: 200px; object-fit: cover; cursor: pointer;"
                                     onclick="showImageModal('{{ asset('storage/' . $image->image_path) }}')">
                                <div class="position-absolute top-0 end-0 m-2">
                                    <span class="badge bg-dark bg-opacity-75">{{ $loop->iteration }}</span>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif

        <!-- Account Status & Settings -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-gear me-2 text-secondary"></i>Account Status & Settings
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Account Status</label>
                        <div>
                            @if($user->is_suspended ?? false)
                                <span class="badge bg-danger-subtle text-danger">
                                    <i class="bi bi-pause-circle me-1"></i>Suspended
                                </span>
                            @else
                                <span class="badge bg-success-subtle text-success">
                                    <i class="bi bi-check-circle me-1"></i>Active
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Payment Status</label>
                        <div>
                            @if($user->paid_at)
                                <span class="badge bg-success-subtle text-success">
                                    <i class="bi bi-check-circle me-1"></i>Paid
                                </span>
                                <small class="text-muted d-block">{{ $user->paid_at->format('M d, Y') }}</small>
                            @else
                                <span class="badge bg-warning-subtle text-warning">
                                    <i class="bi bi-exclamation-circle me-1"></i>Not Paid
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Profile Visibility</label>
                        <div>
                            @if($user->is_public_profile ?? true)
                                <span class="badge bg-info-subtle text-info">
                                    <i class="bi bi-eye me-1"></i>Public
                                </span>
                            @else
                                <span class="badge bg-secondary-subtle text-secondary">
                                    <i class="bi bi-eye-slash me-1"></i>Private
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Last Updated</label>
                        <div>{{ $user->updated_at->format('M d, Y H:i') }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sugar Partner Exchange History -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-arrow-left-right me-2 text-info"></i>Exchange History
                </h6>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#initiateExchangeModal">
                    <i class="bi bi-plus-circle me-1"></i>Initiate Exchange
                </button>
            </div>
            <div class="card-body">
                @php
                    $userExchanges = $user->sugarPartnerExchanges()->with(['user1', 'user2', 'initiatedByAdmin', 'rejections'])->orderBy('created_at', 'desc')->limit(5)->get();
                @endphp

                @if($userExchanges->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Exchange</th>
                                    <th>Partner</th>
                                    <th>Status</th>
                                    <th>Response</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($userExchanges as $exchange)
                                    @php
                                        $partner = $exchange->getOtherUser($user->id);
                                        $userResponse = $exchange->getUserResponse($user->id);
                                    @endphp
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary">#{{ $exchange->id }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-semibold">{{ $partner->name }}</span>
                                                <small class="text-muted">{{ $partner->email }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            @php
                                                $statusClass = match($exchange->status) {
                                                    'pending_payment' => 'bg-warning',
                                                    'payment_completed' => 'bg-info',
                                                    'profiles_viewed' => 'bg-primary',
                                                    'responses_completed' => 'bg-success',
                                                    'cancelled' => 'bg-danger',
                                                    default => 'bg-secondary'
                                                };
                                            @endphp
                                            <span class="badge {{ $statusClass }}">
                                                {{ ucfirst(str_replace('_', ' ', $exchange->status)) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($userResponse)
                                                <span class="badge {{ $userResponse->getRejectionTypeBadgeClass() }}">
                                                    {{ $userResponse->getRejectionTypeDisplayName() }}
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">Pending</span>
                                            @endif
                                        </td>
                                        <td>{{ $exchange->created_at->format('M d, Y') }}</td>
                                        <td>
                                            <a href="{{ route('admin.sugar-partners.exchanges.show', $exchange) }}"
                                               class="btn btn-outline-primary btn-sm"
                                               title="View Exchange" data-bs-toggle="tooltip">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="text-center mt-3">
                        <a href="{{ route('admin.sugar-partners.exchanges.index', ['search' => $user->email]) }}" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-list me-1"></i>View All Exchanges
                        </a>
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="bi bi-arrow-left-right text-muted display-6"></i>
                        <p class="text-muted mt-2 mb-0">No exchange history found for this user.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Initiate Exchange Modal -->
<div class="modal fade" id="initiateExchangeModal" tabindex="-1" aria-labelledby="initiateExchangeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="initiateExchangeModalLabel">
                    <i class="bi bi-arrow-left-right me-2"></i>Initiate Profile Exchange
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ route('admin.sugar-partners.initiate-exchange') }}">
                @csrf
                <input type="hidden" name="user1_id" value="{{ $user->id }}">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Initiating exchange for:</strong> {{ $user->name }} ({{ $user->email }})
                    </div>

                    <div class="mb-3">
                        <label for="user2_id" class="form-label">Select Partner</label>
                        <select class="form-select" id="user2_id" name="user2_id" required>
                            <option value="">Choose a Sugar Partner user...</option>
                            @php
                                $otherSugarPartners = \App\Models\User::where(function($q) {
                                    $q->where('interested_in_sugar_partner', true)
                                      ->orWhereNotNull('sugar_partner_types');
                                })->where('id', '!=', $user->id)->orderBy('name')->get();
                            @endphp
                            @foreach($otherSugarPartners as $partner)
                                <option value="{{ $partner->id }}">
                                    {{ $partner->name }} ({{ $partner->email }})
                                    @if($partner->getWhatIAmForAdmin() !== 'None')
                                        - {{ $partner->getWhatIAmForAdmin() }}
                                    @endif
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes (Optional)</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" placeholder="Add any notes about this exchange..."></textarea>
                    </div>

                    @php
                        $sugarPartnerFeature = \App\Models\Feature::where('name', 'sugar_partner')->first();
                        $exchangePrice = $sugarPartnerFeature?->options['default_exchange_price'] ?? 100.00;
                        $currency = $sugarPartnerFeature?->options['currency'] ?? 'INR';
                    @endphp

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="bi bi-currency-rupee me-2"></i>Exchange Pricing
                        </h6>
                        <p class="mb-0">
                            Both users will be charged <strong>{{ $currency }} {{ number_format($exchangePrice, 2) }}</strong> each for this profile exchange.
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-arrow-left-right me-1"></i>Initiate Exchange
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Gallery Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Gallery Image" class="img-fluid rounded">
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    new bootstrap.Modal(document.getElementById('imageModal')).show();
}

// User action handlers
document.addEventListener('DOMContentLoaded', function() {
    // Suspend user
    document.querySelectorAll('.btn-suspend-user').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.dataset.userId;
            if (confirm('Are you sure you want to suspend this user?')) {
                // Add your suspend user logic here
                console.log('Suspending user:', userId);
            }
        });
    });

    // Activate user
    document.querySelectorAll('.btn-activate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.dataset.userId;
            if (confirm('Are you sure you want to activate this user?')) {
                // Add your activate user logic here
                console.log('Activating user:', userId);
            }
        });
    });
});
</script>
@endpush
