@extends('layouts.admin')

@section('page-title', 'Sugar Partners Management')

@section('page-description', 'Manage Sugar Partners, view profiles, and monitor sugar partner activity across the platform.')

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <span class="text-muted">Sugar Partners</span>
    </li>
@endsection

@section('header-actions')
    <div class="d-flex gap-2">
        <a href="{{ route('admin.sugar-partners.exchanges.index') }}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left-right me-2"></i>View Exchanges
        </a>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#initiateExchangeModal">
            <i class="bi bi-plus-circle me-2"></i>Initiate Exchange
        </button>
        <button type="button" class="btn btn-outline-secondary">
            <i class="bi bi-download me-2"></i>Export Sugar Partners
        </button>
    </div>
@endsection

@section('content')
    <!-- Filters and Search -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.sugar-partners.index') }}" id="filterForm">
                <div class="row g-3 align-items-end">
                    <div class="col-lg-3">
                        <label class="form-label fw-medium">Search</label>
                        <div class="input-group">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="bi bi-search text-muted"></i>
                            </span>
                            <input type="text" name="search" class="form-control border-start-0"
                                   placeholder="Search by name, email, or phone..."
                                   value="{{ request('search') }}" style="box-shadow: none;">
                        </div>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Gender</label>
                        <select name="gender" class="form-select">
                            <option value="">All Genders</option>
                            <option value="male" {{ request('gender') === 'male' ? 'selected' : '' }}>Male</option>
                            <option value="female" {{ request('gender') === 'female' ? 'selected' : '' }}>Female</option>
                        </select>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Sugar Partner Type</label>
                        <select name="sugar_partner_type" class="form-select">
                            <option value="">All Types</option>
                            @foreach($sugarPartnerTypes as $value => $label)
                                <option value="{{ $value }}" {{ request('sugar_partner_type') === $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-lg-2">
                        <label class="form-label fw-medium">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                    </div>
                    <div class="col-lg-3">
                        <label class="form-label fw-medium">Registration Date Range</label>
                        <div class="row g-2">
                            <div class="col-6">
                                <input type="date" name="date_from" class="form-control form-control-sm"
                                       value="{{ request('date_from') }}" placeholder="From">
                            </div>
                            <div class="col-6">
                                <input type="date" name="date_to" class="form-control form-control-sm"
                                       value="{{ request('date_to') }}" placeholder="To">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-3 align-items-end mt-2">
                    <div class="col-lg-12">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-funnel me-1"></i>Filter
                            </button>
                            <a href="{{ route('admin.sugar-partners.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise me-1"></i>Clear Filters
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Hidden sorting inputs -->
                <input type="hidden" name="sort_by" value="{{ request('sort_by', 'created_at') }}">
                <input type="hidden" name="sort_order" value="{{ request('sort_order', 'desc') }}">
            </form>
        </div>
    </div>

    <!-- Sugar Partners Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0 fw-bold">
                    <i class="bi bi-heart me-2 text-danger"></i>Sugar Partners (<span id="sugar-partners-count">{{ $users->total() ?? $users->count() }}</span>)
                </h5>
                <div class="d-flex align-items-center gap-3">
                    <!-- Bulk Actions -->
                    <div class="d-none" id="bulk-actions">
                        <select class="form-select form-select-sm">
                            <option>Bulk Actions</option>
                            <option>Export Selected</option>
                            <option>Suspend Selected</option>
                            <option>Activate Selected</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-download me-2"></i>Export
                    </button>
                </div>
            </div>
        </div>

        <div id="sugar-partners-table">
            @include('admin.sugar-partners.table', ['users' => $users])
        </div>
    </div>
@endsection

<!-- Initiate Exchange Modal -->
<div class="modal fade" id="initiateExchangeModal" tabindex="-1" aria-labelledby="initiateExchangeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="initiateExchangeModalLabel">
                    <i class="bi bi-arrow-left-right me-2"></i>Initiate Profile Exchange
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ route('admin.sugar-partners.initiate-exchange') }}">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Profile Exchange:</strong> Both selected users will be required to pay the configured exchange fee before they can view each other's profiles and submit responses.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user1_id" class="form-label">First User</label>
                                <select class="form-select" id="user1_id" name="user1_id" required>
                                    <option value="">Select first user...</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">
                                            {{ $user->name }} ({{ $user->email }})
                                            @if($user->getWhatIAmForAdmin() !== 'None')
                                                - {{ $user->getWhatIAmForAdmin() }}
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="user2_id" class="form-label">Second User</label>
                                <select class="form-select" id="user2_id" name="user2_id" required>
                                    <option value="">Select second user...</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">
                                            {{ $user->name }} ({{ $user->email }})
                                            @if($user->getWhatIAmForAdmin() !== 'None')
                                                - {{ $user->getWhatIAmForAdmin() }}
                                            @endif
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes (Optional)</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" placeholder="Add any notes about this exchange..."></textarea>
                    </div>

                    @php
                        $sugarPartnerFeature = \App\Models\Feature::where('name', 'sugar_partner')->first();
                        $exchangePrice = $sugarPartnerFeature?->options['default_exchange_price'] ?? 100.00;
                        $currency = $sugarPartnerFeature?->options['currency'] ?? 'INR';
                    @endphp

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="bi bi-currency-rupee me-2"></i>Exchange Pricing
                        </h6>
                        <p class="mb-1">
                            <strong>Price per user:</strong> {{ $currency }} {{ number_format($exchangePrice, 2) }}
                        </p>
                        <p class="mb-0">
                            Both users must pay this amount before accessing profiles. You can modify the default price in the Features settings.
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-arrow-left-right me-1"></i>Initiate Exchange
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('#filterForm select');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });

    // Auto-submit form on date change
    const dateInputs = document.querySelectorAll('#filterForm input[type="date"]');
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });
});

function loadPage(page) {
    const currentFilters = {
        search: document.querySelector('input[name="search"]')?.value || '',
        gender: document.querySelector('select[name="gender"]')?.value || '',
        sugar_partner_type: document.querySelector('select[name="sugar_partner_type"]')?.value || '',
        status: document.querySelector('select[name="status"]')?.value || '',
        date_from: document.querySelector('input[name="date_from"]')?.value || '',
        date_to: document.querySelector('input[name="date_to"]')?.value || '',
        sort_by: document.querySelector('input[name="sort_by"]')?.value || 'created_at',
        sort_order: document.querySelector('input[name="sort_order"]')?.value || 'desc',
        page: page
    };

    // Build query string
    const queryString = Object.keys(currentFilters)
        .filter(key => currentFilters[key])
        .map(key => `${key}=${encodeURIComponent(currentFilters[key])}`)
        .join('&');

    // Fetch new data
    fetch(`{{ route('admin.sugar-partners.data') }}?${queryString}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('sugar-partners-table').innerHTML = html;
            
            // Update count
            const countMatch = html.match(/Showing.*?of.*?(\d+).*?results/);
            if (countMatch) {
                document.getElementById('sugar-partners-count').textContent = countMatch[1];
            }
        })
        .catch(error => {
            console.error('Error loading page:', error);
        });
}

function sortTable(column) {
    const sortByInput = document.querySelector('input[name="sort_by"]');
    const sortOrderInput = document.querySelector('input[name="sort_order"]');

    // If clicking the same column, toggle order
    if (sortByInput.value === column) {
        sortOrderInput.value = sortOrderInput.value === 'asc' ? 'desc' : 'asc';
    } else {
        // New column, default to ascending
        sortByInput.value = column;
        sortOrderInput.value = 'asc';
    }

    document.getElementById('filterForm').submit();
}
</script>
@endpush
