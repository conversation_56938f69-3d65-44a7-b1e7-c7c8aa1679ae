@extends('layouts.admin')

@section('page-title', 'Features Management')
@section('page-description', 'Enable or disable application features')

@section('breadcrumbs')
    <li class="breadcrumb-item active">Features</li>
@endsection

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-toggles2 me-2"></i>Application Features
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshFeatures()">
                        <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Feature</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($features as $feature)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="feature-icon me-3">
                                                @if($feature->name === 'subscription_model')
                                                    <i class="bi bi-credit-card-fill text-success"></i>
                                                @elseif($feature->name === 'sugar_partner')
                                                    <i class="bi bi-heart-fill text-danger"></i>
                                                @elseif($feature->name === 'partner_swapping')
                                                    <i class="bi bi-heart-fill text-purple"></i>
                                                @elseif($feature->name === 'time_spending')
                                                    <i class="bi bi-clock-fill text-success"></i>
                                                @elseif($feature->name === 'gallery')
                                                    <i class="bi bi-images text-info"></i>
                                                @elseif($feature->name === 'meeting_events')
                                                    <i class="bi bi-calendar-event text-purple"></i>
                                                @elseif($feature->name === 'chat_system')
                                                    <i class="bi bi-chat-dots-fill text-primary"></i>
                                                @elseif($feature->name === 'notifications')
                                                    <i class="bi bi-bell-fill text-warning"></i>
                                                @elseif($feature->name === 'user_verification')
                                                    <i class="bi bi-patch-check-fill text-success"></i>
                                                @elseif($feature->name === 'premium_membership')
                                                    <i class="bi bi-star-fill text-warning"></i>
                                                @elseif($feature->name === 'location_services')
                                                    <i class="bi bi-geo-alt-fill text-danger"></i>
                                                @elseif($feature->name === 'privacy_controls')
                                                    <i class="bi bi-shield-fill-check text-success"></i>
                                                @elseif($feature->name === 'meeting_verification')
                                                    <i class="bi bi-camera-fill text-info"></i>
                                                @elseif($feature->name === 'rating_review_system')
                                                    <i class="bi bi-star-fill text-warning"></i>
                                                @else
                                                    <i class="bi bi-gear-fill text-primary"></i>
                                                @endif
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $feature->label }}</h6>
                                                <small class="text-muted">{{ $feature->name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="mb-0">{{ $feature->description }}</p>
                                        @if($feature->options)
                                            <small class="text-muted">
                                                Options: {{ implode(', ', array_values($feature->options)) }}
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge {{ $feature->is_enabled ? 'bg-success' : 'bg-secondary' }}">
                                            {{ $feature->is_enabled ? 'Enabled' : 'Disabled' }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($feature->is_enabled)
                                            <form method="POST" action="{{ route('admin.features.update-status', $feature) }}" style="display: inline;">
                                                @csrf
                                                <input type="hidden" name="is_enabled" value="0">
                                                <button type="submit" class="btn btn-success btn-sm">
                                                    <i class="bi bi-toggle-on me-1"></i>Enabled
                                                </button>
                                            </form>
                                        @else
                                            <form method="POST" action="{{ route('admin.features.update-status', $feature) }}" style="display: inline;">
                                                @csrf
                                                <input type="hidden" name="is_enabled" value="1">
                                                <button type="submit" class="btn btn-secondary btn-sm">
                                                    <i class="bi bi-toggle-off me-1"></i>Disabled
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-toggles2 display-4 d-block mb-3"></i>
                                            <h5>No Features Found</h5>
                                            <p>No application features are configured yet.</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function refreshFeatures() {
    window.location.reload();
}
</script>
@endpush
