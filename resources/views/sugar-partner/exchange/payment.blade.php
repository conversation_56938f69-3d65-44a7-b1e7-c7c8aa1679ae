@extends('layouts.app')

@section('title', 'Sugar Partner Exchange Payment')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-heart-fill me-2"></i>Sugar Partner Profile Exchange Payment
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Exchange Details -->
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-info-circle fs-4 me-3"></i>
                            <div>
                                <h6 class="alert-heading mb-1">Profile Exchange Request</h6>
                                <p class="mb-0">
                                    An admin has initiated a profile exchange between you and <strong>{{ $otherUser->name }}</strong>. 
                                    Both users must complete payment before viewing each other's profiles.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="bi bi-currency-rupee fs-1 text-primary"></i>
                                    <h3 class="text-primary">{{ $exchange->currency }} {{ number_format($exchange->exchange_price, 2) }}</h3>
                                    <p class="text-muted mb-0">Exchange Fee</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="bi bi-people fs-1 text-success"></i>
                                    <h5 class="text-success">{{ $otherUser->name }}</h5>
                                    <p class="text-muted mb-0">Exchange Partner</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div class="mb-4">
                        <h5 class="mb-3">Choose Payment Method</h5>
                        
                        <!-- Wallet Payment Option -->
                        @php
                            $wallet = $user->getWallet();
                            $canUseWallet = $wallet->balance >= $exchange->exchange_price;
                        @endphp
                        
                        <div class="card mb-3 {{ $canUseWallet ? '' : 'opacity-50' }}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-wallet2 fs-4 text-success me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Wallet Payment</h6>
                                            <small class="text-muted">
                                                Current Balance: ₹{{ number_format($wallet->balance, 2) }}
                                            </small>
                                        </div>
                                    </div>
                                    @if($canUseWallet)
                                        <button type="button" class="btn btn-success" onclick="payWithWallet()">
                                            <i class="bi bi-check-circle me-1"></i>Pay with Wallet
                                        </button>
                                    @else
                                        <span class="badge bg-danger">Insufficient Balance</span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Razorpay Payment Option -->
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-credit-card fs-4 text-primary me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Card/UPI Payment</h6>
                                            <small class="text-muted">Pay securely with Razorpay</small>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-primary" onclick="payWithRazorpay()">
                                        <i class="bi bi-credit-card me-1"></i>Pay Now
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Important Notes -->
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="bi bi-exclamation-triangle me-2"></i>Important Notes:
                        </h6>
                        <ul class="mb-0">
                            <li>Both you and {{ $otherUser->name }} must complete payment</li>
                            <li>After payment, you can view each other's complete profiles</li>
                            <li>Private profiles become viewable after successful payment</li>
                            <li>You will be able to respond with Accept, Soft Reject, or Hard Reject</li>
                        </ul>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex gap-2 justify-content-between">
                        <a href="{{ route('sugar-partner.exchange.status', $exchange) }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>Back to Status
                        </a>
                        <a href="{{ route('dashboard') }}" class="btn btn-outline-primary">
                            <i class="bi bi-house me-1"></i>Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden Forms for Payment Processing -->
<form id="walletPaymentForm" method="POST" action="{{ route('sugar-partner.exchange.process-payment', $exchange) }}" style="display: none;">
    @csrf
    <input type="hidden" name="payment_method" value="wallet">
</form>

<form id="razorpayPaymentForm" method="POST" action="{{ route('sugar-partner.exchange.process-payment', $exchange) }}" style="display: none;">
    @csrf
    <input type="hidden" name="payment_method" value="razorpay">
    <input type="hidden" name="razorpay_payment_id" id="razorpay_payment_id">
    <input type="hidden" name="razorpay_order_id" id="razorpay_order_id">
    <input type="hidden" name="razorpay_signature" id="razorpay_signature">
</form>
@endsection

@push('scripts')
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
function payWithWallet() {
    if (confirm('Are you sure you want to pay ₹{{ $exchange->exchange_price }} from your wallet?')) {
        document.getElementById('walletPaymentForm').submit();
    }
}

function payWithRazorpay() {
    var options = {
        "key": "{{ $razorpayKey }}",
        "amount": {{ $exchange->exchange_price * 100 }}, // Amount in paise
        "currency": "{{ $exchange->currency }}",
        "name": "{{ config('app.name') }}",
        "description": "Sugar Partner Profile Exchange",
        "image": "{{ asset('images/logo.png') }}",
        "handler": function (response) {
            document.getElementById('razorpay_payment_id').value = response.razorpay_payment_id;
            document.getElementById('razorpay_order_id').value = response.razorpay_order_id || '';
            document.getElementById('razorpay_signature').value = response.razorpay_signature || '';
            document.getElementById('razorpayPaymentForm').submit();
        },
        "prefill": {
            "name": "{{ $user->name }}",
            "email": "{{ $user->email }}",
            "contact": "{{ $user->contact_number }}"
        },
        "theme": {
            "color": "#007bff"
        }
    };
    
    var rzp = new Razorpay(options);
    rzp.open();
}
</script>
@endpush
