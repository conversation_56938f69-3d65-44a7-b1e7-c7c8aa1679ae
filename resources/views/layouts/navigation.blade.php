<!-- Romantic Header -->
<nav x-data="{ open: false }" class="romantic-header">
    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center" style="min-height: 80px;">
            <!-- Left Side: Logo & Brand -->
            <div class="flex items-center">
                <!-- Enhanced Romantic Logo -->
                <div class="flex-shrink-0">
                    <a href="{{ url('/') }}" class="flex items-center no-underline">
                        @if(App\Models\Setting::get('header_logo'))
                            <!-- Custom Header Logo -->
                            <div class="romantic-logo-container">
                                <img src="{{ asset('storage/' . App\Models\Setting::get('header_logo')) }}"
                                     alt="{{ config('app.name', 'SettingWala') }}"
                                     class="romantic-logo-img">
                            </div>
                        @else
                            <!-- Enhanced Romantic Logo -->
                            <div class="romantic-logo-icon">
                                <i data-lucide="heart" style="width: 40px; height: 40px; color: white;"></i>
                            </div>
                        @endif


                    </a>
                </div>

                <!-- Enhanced Romantic Navigation Links - Only show when authenticated -->
                @auth
                <div class="hidden md:flex items-center gap-2 ml-4">
                    <!-- Home -->
                    <a href="{{ route('home') }}" class="romantic-nav-link {{ request()->routeIs('home') ? 'romantic-nav-link-active' : '' }}">
                        <i data-lucide="home" style="width: 18px; height: 18px;"></i>
                        <span>Home</span>
                    </a>
                        @if(\App\Models\Feature::isEnabled('meeting_events'))
                            <!-- Events -->
                            <a href="{{ route('event.address') }}" class="romantic-nav-link {{ request()->routeIs('event.address*') ? 'romantic-nav-link-active' : '' }}">
                                <i data-lucide="calendar" style="width: 18px; height: 18px;"></i>
                                <span>Events</span>
                            </a>
                        @endif

                        @if(\App\Models\Feature::isEnabled('time_spending'))
                            <!-- Find Person -->
                            <a href="{{ route('find-person.index') }}" class="romantic-nav-link {{ request()->routeIs('find-person.*') ? 'romantic-nav-link-active' : '' }}">
                                <i data-lucide="users" style="width: 18px; height: 18px;"></i>
                                <span>Find Person</span>
                            </a>
                        @endif

                        @if(\App\Models\Feature::isEnabled('time_spending') && auth()->user()->hasActiveChatSessions())
                            <!-- Chat -->
                            <a href="{{ route('chat.index') }}" class="romantic-nav-link {{ request()->routeIs('chat.*') ? 'romantic-nav-link-active' : '' }}">
                                <i data-lucide="message-circle" style="width: 18px; height: 18px;"></i>
                                <span>Chat</span>
                            </a>
                        @endif

                        @if(\App\Models\Feature::isEnabled('partner_swapping') && auth()->user()->is_couple_activity_enabled)
                            <!-- Couple Activity -->
                            <a href="{{ route('couple-activity.index') }}" class="romantic-nav-link {{ request()->routeIs('couple-activity.*') ? 'romantic-nav-link-active' : '' }}">
                                <i data-lucide="heart-handshake" style="width: 18px; height: 18px;"></i>
                                <span>Couple Activity</span>
                            </a>
                        @endif

                        @if (Auth::user()->isAdmin())
                            <!-- Admin -->
                            <a href="{{ route('admin.dashboard') }}" class="romantic-nav-link {{ request()->routeIs('admin.*') ? 'romantic-nav-link-active' : '' }}">
                                <i data-lucide="settings" style="width: 18px; height: 18px;"></i>
                                <span>Admin</span>
                            </a>
                        @endif
                </div>
                @endauth
            </div>

            <!-- Right Side: User Actions -->
            <div class="hidden md:flex items-center gap-3">
                @auth
                    <!-- Notifications (Icon Only) -->
                    @if(\App\Models\Feature::isEnabled('notifications'))
                        <div class="relative inline-block">
                            <a href="{{ route('notifications.index') }}" class="romantic-notification-btn relative {{ request()->routeIs('notifications.*') ? 'romantic-notification-active' : '' }}" title="Notifications">
                                <i data-lucide="bell" style="width: 20px; height: 20px;"></i>
                            </a>
                            <!-- Notification badge -->
                            <span id="notification-badge" class="notification-badge"></span>
                        </div>
                    @endif

                    <!-- Romantic Profile Settings Dropdown -->
                    <div class="relative" x-data="{ open: false }" @click.outside="open = false" style="z-index: 99999;">
                        <!-- Profile Button -->
                        <button @click="open = !open" class="romantic-profile-btn {{ request()->routeIs('profile.*') || request()->routeIs('calendar.*') || request()->routeIs('transactions.*') ? 'romantic-profile-active' : '' }}">
                            @if(Auth::user()->profile_picture_url && Auth::user()->profile_picture_url !== '/images/default-avatar.png')
                                <img src="{{ Auth::user()->profile_picture_url }}"
                                     alt="{{ Auth::user()->name }}"
                                     class="romantic-avatar-small"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <!-- Fallback SVG (hidden by default) -->
                                <div class="romantic-avatar-fallback" style="display: none;">
                                    <i data-lucide="user" style="width: 18px; height: 18px;"></i>
                                </div>
                            @else
                                <!-- Default SVG Avatar -->
                                <div class="romantic-avatar-fallback">
                                    <i data-lucide="user" style="width: 18px; height: 18px;"></i>
                                </div>
                            @endif
                            <span class="romantic-profile-name">{{ explode(' ', Auth::user()->name)[0] }}</span>
                            <i data-lucide="chevron-down" style="width: 16px; height: 16px;" :class="{'rotate-180': open}"></i>
                        </button>

                        <!-- Romantic Dropdown Menu -->
                        <div x-show="open" @click.away="open = false" x-transition class="romantic-dropdown-menu" style="display: none;" x-cloak>
                            <!-- Profile Settings -->
                            <a href="{{ route('profile.edit') }}" @click="open = false" class="romantic-dropdown-item {{ request()->routeIs('profile.edit') ? 'romantic-dropdown-active' : '' }}">
                                <i data-lucide="user" style="width: 18px; height: 18px;"></i>
                                Profile Settings
                            </a>

                            @if(auth()->user()->is_time_spending_enabled)
                                <!-- Booking (Calendar) -->
                                <a href="{{ route('calendar.index') }}" @click="open = false" class="romantic-dropdown-item {{ request()->routeIs('calendar.*') ? 'romantic-dropdown-active' : '' }}">
                                    <i data-lucide="calendar-heart" style="width: 18px; height: 18px;"></i>
                                    My Bookings
                                </a>
                            @endif

                            <!-- Wallet -->
                            <a href="{{ route('wallet.index') }}" @click="open = false" class="romantic-dropdown-item {{ request()->routeIs('wallet.*') ? 'romantic-dropdown-active' : '' }}">
                                <i data-lucide="wallet" style="width: 18px; height: 18px;"></i>
                                Wallet
                            </a>

                            <!-- Transactions -->
                            <a href="{{ route('wallet.index') }}" @click="open = false" class="romantic-dropdown-item {{ request()->routeIs('wallet.*') || request()->routeIs('transactions.*') || request()->routeIs('payment.*') ? 'romantic-dropdown-active' : '' }}">
                                <i data-lucide="credit-card" style="width: 18px; height: 18px;"></i>
                                Transactions
                            </a>

                            <div class="romantic-dropdown-divider"></div>

                            <!-- Logout -->
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" @click="open = false" class="romantic-dropdown-item romantic-dropdown-danger w-100 text-start border-0 bg-transparent">
                                    <i data-lucide="log-out" style="width: 18px; height: 18px;"></i>
                                    Sign Out
                                </button>
                            </form>
                        </div>
                    </div>
                @else
                    <!-- Guest Login Button -->
                    <div class="flex items-center">
                        <a href="{{ route('login') }}" class="header-login-btn">
                            <i data-lucide="log-in"></i>
                            <span>Login</span>
                        </a>
                    </div>
                @endauth
            </div>

            <!-- Mobile Hamburger -->
            <div class="flex items-center md:hidden">
                <button @click="open = ! open" class="romantic-mobile-toggle">
                    <i data-lucide="menu" :class="{'hidden': open, 'block': ! open }" style="width: 24px; height: 24px;"></i>
                    <i data-lucide="x" :class="{'hidden': ! open, 'block': open }" style="width: 24px; height: 24px;"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Responsive Navigation Menu (Mobile) -->
    <div :class="{'block': open, 'hidden': ! open}" class="hidden md:hidden mobile-menu">
        <div class="mobile-menu-content">
            @auth
            <!-- Home (only visible when authenticated) -->
            <a href="{{ route('home') }}" class="mobile-nav-item {{ request()->routeIs('home') ? 'mobile-nav-item-active' : '' }}">
                <div class="mobile-nav-icon">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                </div>
                <span class="mobile-nav-text">Home</span>
            </a>
                @if(\App\Models\Feature::isEnabled('meeting_events'))
                    <!-- Events -->
                    <a href="{{ route('event.address') }}" class="mobile-nav-item {{ request()->routeIs('event.address*') ? 'mobile-nav-item-active' : '' }}">
                        <div class="mobile-nav-icon">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <span class="mobile-nav-text">Events</span>
                    </a>
                @endif

                @if(\App\Models\Feature::isEnabled('time_spending'))
                    <!-- Find Person -->
                    <a href="{{ route('find-person.index') }}" class="mobile-nav-item {{ request()->routeIs('find-person.*') ? 'mobile-nav-item-active' : '' }}">
                        <div class="mobile-nav-icon">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <span class="mobile-nav-text">Find Person</span>
                    </a>

                    @if(auth()->user()->is_time_spending_enabled)
                        <!-- Calendar -->
                        <a href="{{ route('calendar.index') }}" class="mobile-nav-item {{ request()->routeIs('calendar.*') ? 'mobile-nav-item-active' : '' }}">
                            <div class="mobile-nav-icon">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <span class="mobile-nav-text">Calendar</span>
                        </a>
                    @endif
                @endif

                @if(\App\Models\Feature::isEnabled('partner_swapping') && auth()->user()->is_couple_activity_enabled)
                    <!-- Couple Activity -->
                    <a href="{{ route('couple-activity.index') }}" class="mobile-nav-item {{ request()->routeIs('couple-activity.*') ? 'mobile-nav-item-active' : '' }}">
                        <div class="mobile-nav-icon">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <span class="mobile-nav-text">Couple Activity</span>
                    </a>
                @endif

                <!-- Wallet -->
                <a href="{{ route('wallet.index') }}" class="mobile-nav-item {{ request()->routeIs('wallet.*') ? 'mobile-nav-item-active' : '' }}">
                    <div class="mobile-nav-icon">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 1v6m8-6v6"></path>
                        </svg>
                    </div>
                    <span class="mobile-nav-text">Wallet</span>
                </a>

                <!-- Payment -->
                <a href="{{ route('payment.index') }}" class="mobile-nav-item {{ request()->routeIs('payment.*') ? 'mobile-nav-item-active' : '' }}">
                    <div class="mobile-nav-icon">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                    </div>
                    <span class="mobile-nav-text">Payment</span>
                </a>



                @if(\App\Models\Feature::isEnabled('notifications'))
                    <!-- Notifications -->
                    <a href="{{ route('notifications.index') }}" class="mobile-nav-item {{ request()->routeIs('notifications.*') ? 'mobile-nav-item-active' : '' }}">
                        <div class="mobile-nav-icon relative">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                            </svg>
                            <!-- Mobile Notification badge -->
                            <span id="mobile-notification-badge" class="notification-badge"></span>
                        </div>
                        <span class="mobile-nav-text">Notifications</span>
                    </a>
                @endif

                <!-- Profile Settings -->
                <a href="{{ route('profile.edit') }}" class="mobile-nav-item {{ request()->routeIs('profile.*') ? 'mobile-nav-item-active' : '' }}">
                    <div class="mobile-nav-icon">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <span class="mobile-nav-text">Profile Settings</span>
                </a>



                @if (Auth::user()->isAdmin())
                    <!-- Admin -->
                    <a href="{{ route('admin.dashboard') }}" class="mobile-nav-item {{ request()->routeIs('admin.*') ? 'mobile-nav-item-active' : '' }}">
                        <div class="mobile-nav-icon">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <span class="mobile-nav-text">Admin</span>
                    </a>
                @endif
            @else
                <!-- Login for guests -->
                <a href="{{ route('login') }}" class="mobile-nav-item">
                    <div class="mobile-nav-icon">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                        </svg>
                    </div>
                    <span class="mobile-nav-text">Login</span>
                </a>
            @endauth
        </div>

        <!-- Mobile User Profile Section (if authenticated) -->
        @auth
            <div class="pt-4 pb-4 border-t border-warm-200 bg-gradient-to-r from-primary-50 to-blush-50 mx-4 rounded-2xl mb-4">
                <div class="px-4 flex items-center mb-4">
                    @if(Auth::user()->profile_picture_url && Auth::user()->profile_picture_url !== '/images/default-avatar.png')
                        <img src="{{ Auth::user()->profile_picture_url }}"
                             alt="{{ Auth::user()->name }}"
                             class="avatar avatar-lg mr-4"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <!-- Fallback SVG (hidden by default) -->
                        <div class="avatar-circle avatar avatar-lg mr-4 bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center text-white font-semibold" style="display: none;">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    @else
                        <!-- Default SVG Avatar -->
                        <div class="avatar-circle avatar avatar-lg mr-4 bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center text-white font-semibold">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    @endif
                    <div>
                        <div class="font-semibold text-warm-900">{{ explode(' ', Auth::user()->name)[0] }}</div>
                    </div>
                </div>
                <div class="px-4">
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="w-full flex items-center justify-center px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-xl transition-all duration-200 font-medium">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            Sign Out
                        </button>
                    </form>
                </div>
            </div>
        @endauth
    </div>
</nav>






