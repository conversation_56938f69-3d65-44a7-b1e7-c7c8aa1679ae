<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <meta name="color-scheme" content="light only">

        <!-- PWA Meta Tags -->
        <meta name="theme-color" content="{{ App\Models\Setting::get('theme_color', '#C9B6E4') }}">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="apple-mobile-web-app-title" content="{{ config('app.name', 'SettingWala') }}">
        <meta name="mobile-web-app-capable" content="yes">

        <!-- Camera and Location Permissions -->
        <meta http-equiv="Permissions-Policy" content="camera=(self), geolocation=(self), microphone=()">



        <!-- Mobile App Icon -->
        @if(App\Models\Setting::get('mobile_icon'))
            <meta name="mobile-app-icon" content="{{ asset('storage/' . App\Models\Setting::get('mobile_icon')) }}">
        @endif

        <!-- PWA Manifest -->
        <link rel="manifest" href="{{ asset('manifest.json') }}">

        <!-- Apple Touch Icons -->
        @php
            $mobileIcon = App\Models\Setting::get('mobile_icon');
            $iconUrl = $mobileIcon ? asset('storage/' . $mobileIcon) : asset('images/icon-192x192.png');
        @endphp
        <link rel="apple-touch-icon" href="{{ $iconUrl }}">
        <link rel="apple-touch-icon" sizes="152x152" href="{{ $iconUrl }}">
        <link rel="apple-touch-icon" sizes="180x180" href="{{ $iconUrl }}">

        <title>{{ config('app.name', 'SettingWala') }} - @yield('title', 'Find Your Perfect Match')</title>
        <meta name="description" content="@yield('description', 'Connect with like-minded individuals and discover meaningful relationships through verified profiles and local events.')">

        <!-- SEO Meta Tags -->
        <meta name="keywords" content="dating app, relationships, love, dating, singles, matches, events, verified profiles">
        <meta name="author" content="{{ config('app.name', 'SettingWala') }}">
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
        <link rel="canonical" href="{{ $canonicalUrl ?? request()->url() }}">

        <!-- Open Graph Meta Tags -->
        @if(isset($ogData))
            <meta property="og:title" content="{{ $ogData['title'] }}">
            <meta property="og:description" content="{{ $ogData['description'] }}">
            <meta property="og:image" content="{{ $ogData['image'] }}">
            <meta property="og:url" content="{{ $ogData['url'] }}">
            <meta property="og:type" content="{{ $ogData['type'] }}">
            <meta property="og:site_name" content="{{ $ogData['site_name'] }}">
            <meta property="og:locale" content="en_US">
        @endif

        <!-- Twitter Card Meta Tags -->
        @if(isset($twitterData))
            <meta name="twitter:card" content="{{ $twitterData['card'] }}">
            <meta name="twitter:title" content="{{ $twitterData['title'] }}">
            <meta name="twitter:description" content="{{ $twitterData['description'] }}">
            <meta name="twitter:image" content="{{ $twitterData['image'] }}">
        @endif



        <!-- Favicon -->
        @if(App\Models\Setting::get('favicon'))
            <link rel="icon" type="image/x-icon" href="{{ asset('storage/' . App\Models\Setting::get('favicon')) }}">
        @else
            <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
        @endif

        <!-- Service Worker Debug Scripts (disabled for production) -->

        <!-- Structured Data -->
        @if(isset($structuredData))
            <script type="application/ld+json">{!! $structuredData !!}</script>
        @endif

        <!-- Google Fonts - Optimized Loading -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="preload" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Nunito:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
        <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Nunito:wght@300;400;500;600;700&display=swap"></noscript>

        <!-- Tailwind CSS CDN -->
        <script src="https://cdn.tailwindcss.com"></script>
        <script>
            tailwind.config = {
                theme: {
                    extend: {
                        colors: {
                            'romantic-pink': '#4F46E5',
                            'romantic-rose': '#6366F1',
                            'romantic-blush': '#E0E7FF',
                            'romantic-peach': '#F3F4F6',
                        },
                        animation: {
                            'float': 'float 3s ease-in-out infinite',
                            'shimmer': 'shimmer 2s ease-in-out infinite',
                        },
                        keyframes: {
                            float: {
                                '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
                                '50%': { transform: 'translateY(-20px) rotate(5deg)' },
                            },
                            shimmer: {
                                '0%': { transform: 'translateX(-100%)' },
                                '100%': { transform: 'translateX(100%)' },
                            }
                        }
                    }
                }
            }
        </script>

        <!-- Romantic Theme CSS -->
        <link rel="stylesheet" href="{{ asset('css/romantic-events.css') }}?v={{ time() }}">

        <!-- AOS Animation Library - Optimized Loading -->
        <link rel="preload" href="https://unpkg.com/aos@next/dist/aos.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
        <noscript><link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css"></noscript>



        <!-- Alpine.js -->
        <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

        <!-- Preload Critical JavaScript -->
        <link rel="preload" href="https://unpkg.com/lucide@latest" as="script">



    </head>
    <body class="bg-gray-50">
        <div class="min-h-screen">
            @include('layouts.navigation')



            <!-- Flash Messages -->
            @if (session('success'))
                <div class="fixed top-4 right-4 z-50 animate-slide-up" x-data="{ show: true }" x-show="show" x-transition>
                    <div class="bg-green-50 border border-green-200 text-green-800 px-6 py-4 rounded-2xl shadow-soft max-w-sm">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="font-medium">{{ session('success') }}</span>
                            </div>
                            <button @click="show = false" class="ml-4 text-green-500 hover:text-green-700">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            @if (session('error'))
                <div class="fixed top-4 right-4 z-50 animate-slide-up" x-data="{ show: true }" x-show="show" x-transition>
                    <div class="bg-red-50 border border-red-200 text-red-800 px-6 py-4 rounded-2xl shadow-soft max-w-sm">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-3 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="font-medium">{{ session('error') }}</span>
                            </div>
                            <button @click="show = false" class="ml-4 text-red-500 hover:text-red-700">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Page Content -->
            <main>
                @yield('content')
                @isset($slot)
                    {{ $slot }}
                @endisset
            </main>

            <!-- Footer -->
            <x-footer />
        </div>

        <!-- Enhanced Date/Time Picker Initialization -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize enhanced date pickers for better cross-browser support
            function initializeDatePickers() {
                // Get all date inputs
                const dateInputs = document.querySelectorAll('input[type="date"]');
                const timeInputs = document.querySelectorAll('input[type="time"]');
                const datetimeInputs = document.querySelectorAll('input[type="datetime-local"]');

                // Enhanced date picker for Safari and better UX
                dateInputs.forEach(input => {
                    // Check if browser supports date input properly
                    const testInput = document.createElement('input');
                    testInput.type = 'date';
                    const isDateSupported = testInput.type === 'date';

                    // For Safari or browsers with poor date support, use Flatpickr
                    if (!isDateSupported || /^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
                        flatpickr(input, {
                            dateFormat: "Y-m-d",
                            allowInput: true,
                            clickOpens: true,
                            theme: "light",
                            minDate: input.getAttribute('min') || null,
                            maxDate: input.getAttribute('max') || null,
                            defaultDate: input.value || null,
                            onChange: function(selectedDates, dateStr, instance) {
                                // Trigger change event for form validation
                                input.dispatchEvent(new Event('change', { bubbles: true }));
                            }
                        });
                    }

                    // Enhanced click handling for all browsers
                    input.addEventListener('click', function() {
                        if (this.showPicker && typeof this.showPicker === 'function') {
                            try {
                                this.showPicker();
                            } catch (e) {
                                // Fallback for browsers that don't support showPicker
                                this.focus();
                            }
                        } else {
                            this.focus();
                        }
                    });


                });

                // Enhanced time picker
                timeInputs.forEach(input => {
                    // For Safari or browsers with poor time support
                    if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
                        flatpickr(input, {
                            enableTime: true,
                            noCalendar: true,
                            dateFormat: "H:i",
                            time_24hr: false,
                            allowInput: true,
                            clickOpens: true,
                            theme: "light",
                            defaultDate: input.value || null,
                            onChange: function(selectedDates, dateStr, instance) {
                                input.dispatchEvent(new Event('change', { bubbles: true }));
                            }
                        });
                    }

                    // Enhanced click handling
                    input.addEventListener('click', function() {
                        if (this.showPicker && typeof this.showPicker === 'function') {
                            try {
                                this.showPicker();
                            } catch (e) {
                                this.focus();
                            }
                        } else {
                            this.focus();
                        }
                    });


                });

                // Enhanced datetime-local picker
                datetimeInputs.forEach(input => {
                    if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
                        flatpickr(input, {
                            enableTime: true,
                            dateFormat: "Y-m-d H:i",
                            time_24hr: false,
                            allowInput: true,
                            clickOpens: true,
                            theme: "light",
                            minDate: input.getAttribute('min') || null,
                            maxDate: input.getAttribute('max') || null,
                            defaultDate: input.value || null,
                            onChange: function(selectedDates, dateStr, instance) {
                                input.dispatchEvent(new Event('change', { bubbles: true }));
                            }
                        });
                    }

                    // Enhanced click handling
                    input.addEventListener('click', function() {
                        if (this.showPicker && typeof this.showPicker === 'function') {
                            try {
                                this.showPicker();
                            } catch (e) {
                                this.focus();
                            }
                        } else {
                            this.focus();
                        }
                    });


                });
            }

            // Initialize on page load
            initializeDatePickers();

            // Re-initialize when new content is added dynamically (with debouncing for performance)
            let reinitTimeout;
            const observer = new MutationObserver(function(mutations) {
                // Debounce the reinitialization to avoid excessive calls
                clearTimeout(reinitTimeout);
                reinitTimeout = setTimeout(() => {
                    let hasNewInputs = false;
                    mutations.forEach(function(mutation) {
                        if (mutation.addedNodes.length > 0) {
                            // Check if any added nodes contain date/time inputs
                            mutation.addedNodes.forEach(node => {
                                if (node.nodeType === 1) { // Element node
                                    if (node.matches && (
                                        node.matches('input[type="date"], input[type="time"], input[type="datetime-local"]') ||
                                        node.querySelector('input[type="date"], input[type="time"], input[type="datetime-local"]')
                                    )) {
                                        hasNewInputs = true;
                                    }
                                }
                            });
                        }
                    });

                    if (hasNewInputs) {
                        initializeDatePickers();
                    }
                }, 100); // 100ms debounce
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Make function globally available
            window.initializeDatePickers = initializeDatePickers;
        });
        </script>

        <!-- AOS Animation JS -->
        <script src="https://unpkg.com/aos@next/dist/aos.js" defer></script>

        <!-- Lucide Icons -->
        <script src="https://unpkg.com/lucide@latest" defer></script>

        <!-- App JavaScript -->
        <script src="{{ asset('js/app.js') }}" defer></script>

        <!-- Romantic Theme Initialization -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Force refresh theme color meta tag
                const themeColorMeta = document.querySelector('meta[name="theme-color"]');
                if (themeColorMeta) {
                    const currentColor = themeColorMeta.getAttribute('content');
                    // Force browser to re-read the meta tag
                    themeColorMeta.setAttribute('content', '');
                    setTimeout(() => {
                        themeColorMeta.setAttribute('content', currentColor);
                    }, 10);
                }

                // Initialize AOS when available
                if (typeof AOS !== 'undefined') {
                    AOS.init({
                        duration: 800,
                        easing: 'ease-in-out',
                        once: true,
                        offset: 100
                    });
                }

                // Initialize Lucide Icons when available
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }

                // Initialize notification badge when app.js is loaded
                if (typeof initializeNotificationBadge === 'function') {
                    initializeNotificationBadge();
                } else {
                    // Retry after a short delay if app.js hasn't loaded yet
                    setTimeout(() => {
                        if (typeof initializeNotificationBadge === 'function') {
                            initializeNotificationBadge();
                        }
                    }, 500);
                }

                // Button hover animations (using Tailwind classes)
                const buttons = document.querySelectorAll('button, .btn');
                buttons.forEach(button => {
                    if (button.classList.contains('bg-gradient-to-r')) {
                        button.addEventListener('mouseenter', function() {
                            this.classList.add('animate-pulse');
                        });

                        button.addEventListener('mouseleave', function() {
                            this.classList.remove('animate-pulse');
                        });
                    }
                });

                // Countdown Timer Function (if needed)
                window.initCountdownTimer = function(targetDate, elementId) {
                    const countdownElement = document.getElementById(elementId);
                    if (!countdownElement) return;

                    function updateCountdown() {
                        const now = new Date().getTime();
                        const distance = targetDate - now;

                        if (distance < 0) {
                            countdownElement.innerHTML = '<div class="text-center"><h3>Event Started!</h3></div>';
                            return;
                        }

                        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                        countdownElement.innerHTML = `
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="countdown-item">
                                        <span class="countdown-number">${days.toString().padStart(2, '0')}</span>
                                        <span class="countdown-label">Days</span>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="countdown-item">
                                        <span class="countdown-number">${hours.toString().padStart(2, '0')}</span>
                                        <span class="countdown-label">Hours</span>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="countdown-item">
                                        <span class="countdown-number">${minutes.toString().padStart(2, '0')}</span>
                                        <span class="countdown-label">Minutes</span>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="countdown-item">
                                        <span class="countdown-number">${seconds.toString().padStart(2, '0')}</span>
                                        <span class="countdown-label">Seconds</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    updateCountdown();
                    setInterval(updateCountdown, 1000);
                };
            });
        </script>

        @stack('scripts')
    </body>
</html>
